import 'package:easydine_main/widgets/tiled_background.dart';
import 'package:easydine_main/blocs/staff/staff_bloc.dart';
import 'package:easydine_main/blocs/staff/staff_event.dart';
import 'package:easydine_main/blocs/staff/staff_state.dart';
import 'package:easydine_main/blocs/attendance/attendance_bloc.dart';
import 'package:easydine_main/blocs/attendance/attendance_event.dart';
import 'package:easydine_main/blocs/attendance/attendance_state.dart';
import 'package:easydine_main/services/branch_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../blocs/session/session_bloc.dart';
import '../../blocs/session/session_event.dart';
import '../../blocs/session/session_state.dart';
import '../../router/router_constants.dart';
import '../../models/staff_model.dart';
import 'clock_action_page.dart';
import 'package:google_fonts/google_fonts.dart';

class PinEntryPage extends StatefulWidget {
  const PinEntryPage({super.key});

  @override
  State<PinEntryPage> createState() => _PinEntryPageState();
}

class _PinEntryPageState extends State<PinEntryPage> {
  String _pin = '';
  bool _showError = false;
  StaffModel? _selectedStaff;

  @override
  void initState() {
    super.initState();
    _fetchClockedInStaff();
  }

  Future<void> _fetchClockedInStaff() async {
    final branchId = await BranchService.getSelectedBranchId();
    if (branchId != null && mounted) {
      context.read<StaffBloc>().add(FetchClockedInStaff(branchId: branchId));
    }
  }

  void _onKeyPress(String digit) {
    if (_pin.length < 5) {
      setState(() {
        _pin += digit;
        _showError = false;
      });

      if (_pin.length == 5 && _selectedStaff != null) {
        context.read<SessionBloc>().add(
              VerifyPin(
                pin: _pin,
                staffId: _selectedStaff!.id,
              ),
            );
      }
    }
  }

  void _onBackspace() {
    if (_pin.isNotEmpty) {
      setState(() {
        _pin = _pin.substring(0, _pin.length - 1);
        _showError = false;
      });
    }
  }

  Widget _buildStaffList() {
    return BlocBuilder<StaffBloc, StaffState>(
      buildWhen: (previous, current) =>
          previous.clockedInStaff != current.clockedInStaff ||
          previous.status != current.status,
      builder: (context, staffState) {
        if (staffState.status == StaffStatus.loading) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.white10,
              border: Border(
                right: BorderSide(
                  color: Colors.white24,
                  width: 1,
                ),
              ),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.deepOrange),
              ),
            ),
          );
        }

        // Show only clocked-in staff from API
        final staffList = staffState.clockedInStaff;

        return Container(
          decoration: BoxDecoration(
            color: Colors.white10,
            border: Border(
              right: BorderSide(
                color: Colors.white24,
                width: 1,
              ),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Clocked-In Staff',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              if (staffList.isEmpty)
                Expanded(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.person_off_outlined,
                          size: 48,
                          color: Colors.white.withOpacity(0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No Staff Clocked In',
                          style: GoogleFonts.poppins(
                            color: Colors.white70,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else
                Expanded(
                  child: ListView.builder(
                    itemCount: staffList.length,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    itemBuilder: (context, index) {
                      final staff = staffList[index];
                      final isSelected = _selectedStaff?.id == staff.id;
                      final isClockedIn = staffState.clockedInStaff.any(
                          (clockedInStaff) => clockedInStaff.id == staff.id);

                      return Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 4,
                          horizontal: 8,
                        ),
                        child: ListTile(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          selected: isSelected,
                          selectedTileColor: Colors.transparent,
                          leading: CircleAvatar(
                            backgroundColor:
                                isSelected ? Colors.deepOrange : Colors.white24,
                            child: Icon(
                              Icons.person,
                              color: Colors.white,
                            ),
                          ),
                          title: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  staff.name,
                                  style: GoogleFonts.poppins(
                                    color: Colors.white,
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ),
                              ),
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color:
                                      isClockedIn ? Colors.green : Colors.grey,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                            ],
                          ),
                          subtitle: Text(
                            '${staff.role ?? 'Staff'}${isClockedIn ? ' • Clocked In' : ''}',
                            style: GoogleFonts.poppins(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                          trailing: isClockedIn
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    // Check In button
                                    ElevatedButton(
                                      onPressed: () =>
                                          _handleCheckAction(staff, 'checkin'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 4),
                                        minimumSize: const Size(60, 30),
                                      ),
                                      child: Text(
                                        'Check In',
                                        style: GoogleFonts.poppins(
                                          color: Colors.white,
                                          fontSize: 10,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    // Check Out button
                                    ElevatedButton(
                                      onPressed: () =>
                                          _handleCheckAction(staff, 'checkout'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 4),
                                        minimumSize: const Size(60, 30),
                                      ),
                                      child: Text(
                                        'Check Out',
                                        style: GoogleFonts.poppins(
                                          color: Colors.white,
                                          fontSize: 10,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : null,
                          onTap: () {
                            setState(() {
                              _selectedStaff = staff;
                              _pin = '';
                              _showError = false;
                            });
                          },
                        ),
                      );
                    },
                  ),
                ),
              // Clock In/Out Section
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Container(
                  height: 80,
                  width:
                      MediaQuery.of(context).orientation == Orientation.portrait
                          ? MediaQuery.of(context).size.width * 0.35
                          : MediaQuery.of(context).size.width * 0.25,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      colors: [
                        Colors.indigo.withOpacity(0.9),
                        Colors.blue.withOpacity(0.9),
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildClockButton(
                        context,
                        'Clock In',
                        Icons.login_rounded,
                        Colors.green,
                        ClockAction.clockIn,
                      ),
                      Container(
                        width: 1,
                        height: 24,
                        color: Colors.white24,
                      ),
                      _buildClockButton(
                        context,
                        'Clock Out',
                        Icons.logout_rounded,
                        Colors.orange,
                        ClockAction.clockOut,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPinDot(bool filled) {
    return Container(
      margin: const EdgeInsets.all(6),
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: filled ? Colors.deepOrange : Colors.white24,
        border: Border.all(
          color: filled ? Colors.deepOrange : Colors.white24,
          width: 2,
        ),
      ),
    );
  }

  Widget _buildKeypadButton(String value) {
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonSize = screenWidth *
        (MediaQuery.of(context).orientation == Orientation.portrait
            ? 0.08
            : 0.05);

    return SizedBox(
      height: buttonSize,
      width: buttonSize,
      child: TextButton(
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(buttonSize * 0.15),
          ),
          backgroundColor: value.isEmpty ? Colors.transparent : Colors.white10,
        ),
        onPressed: _selectedStaff == null || value.isEmpty
            ? null
            : () {
                if (value == '⌫') {
                  _onBackspace();
                } else {
                  _onKeyPress(value);
                }
              },
        child: value == '⌫'
            ? Icon(
                Icons.backspace_outlined,
                color: Colors.white70,
                size: buttonSize * 0.4,
              )
            : Text(
                value,
                style: GoogleFonts.poppins(
                  fontSize: buttonSize * 0.4,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
      ),
    );
  }

  Widget _buildPinEntry() {
    if (_selectedStaff == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_outline,
              size: 64,
              color: Colors.white.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Select a Staff Member',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }

    final screenWidth = MediaQuery.of(context).size.width;
    final keypadWidth = screenWidth *
        (MediaQuery.of(context).orientation == Orientation.portrait
            ? 0.3
            : 0.2);

    return Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Enter PIN for ${_selectedStaff!.name}',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                5,
                (index) => _buildPinDot(_pin.length > index),
              ),
            ),
            const SizedBox(height: 32),
            if (_showError)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  'Invalid PIN. Please try again.',
                  style: GoogleFonts.poppins(
                    color: Colors.red,
                    fontSize: 14,
                  ),
                ),
              ),
            Container(
              constraints: BoxConstraints(maxWidth: keypadWidth),
              child: Wrap(
                alignment: WrapAlignment.center,
                spacing: 16,
                runSpacing: 16,
                children: [
                  ...List.generate(9, (i) => _buildKeypadButton('${i + 1}')),
                  _buildKeypadButton(''),
                  _buildKeypadButton('0'),
                  _buildKeypadButton('⌫'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String label, String value, Color color, IconData icon) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Text(
            label,
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<SessionBloc, SessionState>(
          listener: (context, state) {
            if (state.status == SessionStatus.authenticated) {
              GoRouter.of(context).goNamed(
                RouterConstants.dailyChecklist,
              );
            } else if (state.status == SessionStatus.error) {
              setState(() {
                _pin = '';
                _showError = true;
              });
            }
          },
        ),
        BlocListener<AttendanceBloc, AttendanceState>(
          listener: (context, state) {
            if (state.status == AttendanceStatus.success) {
              // Show success message for attendance operations
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.successMessage ??
                      'Operation completed successfully'),
                  backgroundColor: Colors.green,
                  duration: const Duration(seconds: 2),
                ),
              );
              // Refresh staff data after attendance operation
              _fetchClockedInStaff();
            } else if (state.status == AttendanceStatus.error) {
              // Show error message for attendance operations
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error ?? 'Operation failed'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 3),
                ),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<SessionBloc, SessionState>(
        builder: (context, state) {
          return Scaffold(
            backgroundColor: Colors.transparent,
            body: Stack(
              children: [
                const TiledBackground(),
                SafeArea(
                  child: Row(
                    children: [
                      // Staff List
                      SizedBox(
                        width: MediaQuery.of(context).orientation ==
                                Orientation.portrait
                            ? MediaQuery.of(context).size.width * 0.35
                            : MediaQuery.of(context).size.width * 0.25,
                        child: _buildStaffList(),
                      ),
                      // PIN Entry or Orders
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: _buildPinEntry(),
                        ),
                      ),
                    ],
                  ),
                ),
                // Loading overlay for attendance operations
                BlocBuilder<AttendanceBloc, AttendanceState>(
                  builder: (context, attendanceState) {
                    if (attendanceState.status == AttendanceStatus.loading) {
                      return Container(
                        color: Colors.black54,
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.deepOrange),
                              ),
                              SizedBox(height: 16),
                              Text(
                                'Processing...',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildClockButton(
    BuildContext context,
    String label,
    IconData icon,
    Color iconColor,
    ClockAction action,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ClockActionPage(action: action),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: iconColor,
                size: 20,
              ),
              const SizedBox(width: 4),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Text(
                  label,
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleCheckAction(StaffModel staff, String action) {
    // Show PIN entry dialog for check-in/check-out
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _CheckActionDialog(
        staff: staff,
        action: action,
        onConfirm: (pin) {
          if (action == 'checkin') {
            context.read<AttendanceBloc>().add(CheckInStaff(
                  staffId: staff.id,
                  pin: pin,
                ));
          } else {
            context.read<AttendanceBloc>().add(CheckOutStaff(
                  staffId: staff.id,
                  pin: pin,
                ));
          }
        },
      ),
    );
  }
}

// Dialog widget for check-in/check-out PIN entry
class _CheckActionDialog extends StatefulWidget {
  final StaffModel staff;
  final String action;
  final Function(String) onConfirm;

  const _CheckActionDialog({
    required this.staff,
    required this.action,
    required this.onConfirm,
  });

  @override
  State<_CheckActionDialog> createState() => _CheckActionDialogState();
}

class _CheckActionDialogState extends State<_CheckActionDialog> {
  String _pin = '';

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.grey[900],
      title: Text(
        '${widget.action == 'checkin' ? 'Check In' : 'Check Out'} ${widget.staff.name}',
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Enter PIN to ${widget.action == 'checkin' ? 'check in' : 'check out'}',
            style: GoogleFonts.poppins(color: Colors.white70),
          ),
          const SizedBox(height: 16),
          TextField(
            onChanged: (value) => setState(() => _pin = value),
            keyboardType: TextInputType.number,
            obscureText: true,
            maxLength: 4,
            style: GoogleFonts.poppins(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'Enter 4-digit PIN',
              hintStyle: GoogleFonts.poppins(color: Colors.white54),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.white54),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.white54),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.blue),
              ),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: GoogleFonts.poppins(color: Colors.white70),
          ),
        ),
        ElevatedButton(
          onPressed: _pin.length == 4
              ? () {
                  Navigator.of(context).pop();
                  widget.onConfirm(_pin);
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor:
                widget.action == 'checkin' ? Colors.blue : Colors.red,
          ),
          child: Text(
            widget.action == 'checkin' ? 'Check In' : 'Check Out',
            style: GoogleFonts.poppins(color: Colors.white),
          ),
        ),
      ],
    );
  }
}
