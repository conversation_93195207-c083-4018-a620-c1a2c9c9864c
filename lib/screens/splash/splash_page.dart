import 'package:easydine_main/blocs/auth/auth_bloc.dart';
import 'package:easydine_main/blocs/auth/auth_state.dart';
import 'package:easydine_main/router/router_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  double _opacity = 0.0;
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _opacity = 1.0;
      });
    });

    // Set a minimum splash duration
    Future.delayed(const Duration(seconds: 2), () {
      // Navigation will be handled by BlocList<PERSON>
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (!_hasNavigated) {
          final router = GoRouter.of(context);
          // Wait for minimum splash duration before navigating
          Future.delayed(const Duration(seconds: 2), () async {
            if (mounted && !_hasNavigated) {
              setState(() {
                _hasNavigated = true;
              });

              print('🚀 Splash: AuthState is ${state.runtimeType}');

              if (state is AuthAuthenticated) {
                // Check if branch is already selected
                final prefs = await SharedPreferences.getInstance();
                final selectedBranchId = prefs.getString('selected_branch_id');

                if (selectedBranchId != null && selectedBranchId.isNotEmpty) {
                  print(
                      '🏢 Splash: Branch already selected, going to pin entry');
                  router.goNamed(RouterConstants.pinEntry);
                } else {
                  print(
                      '🏢 Splash: No branch selected, going to branch selection');
                  router.goNamed(RouterConstants.branchSelection);
                }
              } else {
                print('🔐 Splash: Not authenticated, going to login');
                router.goNamed(RouterConstants.login);
              }
            }
          });
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: AnimatedOpacity(
            opacity: _opacity,
            duration: const Duration(seconds: 2),
            child: Image.asset(
              'assets/logo.jpg',
              width: 200,
              height: 200,
            ),
          ),
        ),
      ),
    );
  }
}
