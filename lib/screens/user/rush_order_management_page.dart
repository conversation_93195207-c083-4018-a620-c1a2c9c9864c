import 'dart:ui';

import 'package:easydine_main/widgets/app_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../blocs/settings/settings_bloc.dart';
import '../../widgets/tiled_background.dart';

class RushOrderManagementPage extends StatefulWidget {
  const RushOrderManagementPage({super.key});

  @override
  State<RushOrderManagementPage> createState() => _RushOrderManagementPageState();
}

class _RushOrderManagementPageState extends State<RushOrderManagementPage> {
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  bool isRushEnabled = false;
  double rushOrderFee = 5.00;
  int rushPreparationTime = 15;

  @override
  Widget build(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      key: scaffoldKey,
      appBar: WaiterAppBar(scaffoldKey: scaffoldKey),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          const TiledBackground(),
          SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(isLandscape ? 24 : 16),
              child: isLandscape
                  ? _buildLandscapeLayout(screenSize)
                  : _buildPortraitLayout(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(Size screenSize) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column - Status and Settings
        Expanded(
          flex: 3,
          child: Column(
            children: [
              _buildStatusCard(),
              const SizedBox(height: 24),
              _buildSettingsSection(),
            ],
          ),
        ),
        const SizedBox(width: 24),
        // Right column - Statistics and Active Orders
        Expanded(
          flex: 4,
          child: Column(
            children: [
              _buildStatisticsCard(),
              const SizedBox(height: 24),
              _buildActiveOrdersCard(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPortraitLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        _buildStatusCard(),
        const SizedBox(height: 20),
        _buildSettingsSection(),
        const SizedBox(height: 20),
        _buildStatisticsCard(),
        const SizedBox(height: 20),
        _buildActiveOrdersCard(),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade800,
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white24),
            onPressed: () => Navigator.pop(context),
          ),
          const SizedBox(width: 8),
          const Icon(Icons.speed_outlined, color: Colors.white24, size: 28),
          const SizedBox(width: 12),
          Text(
            'Rush Order Management',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Colors.white24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Rush Order Status',
              style: GoogleFonts.poppins(
                fontSize: 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            BlocBuilder<SettingsBloc, SettingsState>(
              builder: (context, state) {
                return SwitchListTile(
                  title: Text(
                    'Enable Rush Orders',
                    style: GoogleFonts.poppins(fontSize: 16, color: Colors.white),
                  ),
                  subtitle: Text(
                    state.rushOrderEnabled ? 'Active' : 'Inactive',
                    style: GoogleFonts.poppins(
                      color: state.rushOrderEnabled ? Colors.green : Colors.grey,
                    ),
                  ),
                  value: state.rushOrderEnabled,
                  onChanged: (value) {
                    context
                        .read<SettingsBloc>()
                        .add(ToggleRushOrder(isEnabled: value));
                  },
                  activeColor: Colors.red.shade800,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsSection() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(isLandscape ? 24 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Settings',
              style: GoogleFonts.poppins(
                fontSize: isLandscape ? 22 : 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            _buildSettingTile(
              'Rush Order Fee',
              '\$${rushOrderFee.toStringAsFixed(2)}',
              Icons.attach_money,
              onTap: _editRushFee,
            ),
            const Divider(color: Colors.white24),
            _buildSettingTile(
              'Preparation Time',
              '$rushPreparationTime minutes',
              Icons.timer,
              onTap: _editRushTime,
            ),
            const Divider(color: Colors.white24),
            _buildSettingTile(
              'Auto-accept Orders',
              'Disabled',
              Icons.auto_awesome,
              onTap: () {
                // Handle auto-accept setting
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingTile(
    String title,
    String value,
    IconData icon, {
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.red.shade800),
      title: Text(title, style: GoogleFonts.poppins(
        fontWeight: FontWeight.w600,
        color: Colors.white,
      )),
      subtitle: Text(value, style: GoogleFonts.poppins(fontWeight: FontWeight.w500, color: Colors.grey[600])),
      trailing: const Icon(Icons.edit, color: Colors.white24),
      onTap: onTap,
    );
  }

  Widget _buildStatisticsCard() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(isLandscape ? 24 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Today\'s Statistics',
              style: GoogleFonts.poppins(
                fontSize: isLandscape ? 22 : 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            StaggeredGrid.count(
              crossAxisCount: isLandscape ? 3 : 3,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: [
                _buildStatGridItem('Rush Orders', '12', Icons.speed),
                _buildStatGridItem('Revenue', '\$240', Icons.payments),
                _buildStatGridItem('Avg. Time', '18 min', Icons.timer),
                _buildStatGridItem('Completed', '8', Icons.check_circle),
                _buildStatGridItem('Pending', '4', Icons.pending),
                _buildStatGridItem('Cancelled', '0', Icons.cancel),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatGridItem(String label, String value, IconData icon) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return StaggeredGridTile.extent(
      crossAxisCellCount: 1,
      mainAxisExtent: isLandscape ? 120 : 100,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white.withOpacity(0.1)),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, color: Colors.red.shade800, size: 28),
                  const SizedBox(height: 8),
                  Text(
                    value,
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    label,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActiveOrdersCard() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(isLandscape ? 24 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Active Rush Orders',
                  style: GoogleFonts.poppins(
                    fontSize: isLandscape ? 22 : 20,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton.icon(
                  onPressed: () {
                    // View all orders
                  },
                  icon: const Icon(Icons.visibility, color: Colors.white70),
                  label: Text(
                    'View All',
                    style: GoogleFonts.poppins(color: Colors.white70),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Add your active orders list here
            // Consider using a ListView.builder for dynamic content
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem({
    required String orderId,
    required String tableNumber,
    required String timeRemaining,
    required String orderType,
  }) {
    return Card(
      color: Colors.white24,
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.red.shade50,
          child: Icon(
            orderType == 'Takeaway' ? Icons.takeout_dining : Icons.table_restaurant,
            color: Colors.red.shade800,
          ),
        ),
        title: Text(
          'Order $orderId',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          '$orderType • Table $tableNumber',
          style: GoogleFonts.poppins(),
        ),
        trailing: Chip(
          label: Text(
            timeRemaining,
            style: GoogleFonts.poppins(color: Colors.white),
          ),
          backgroundColor: Colors.red.shade800,
        ),
      ),
    );
  }

  void _editRushFee() {
    TextEditingController feeController =
        TextEditingController(text: rushOrderFee.toStringAsFixed(2));

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            "Set Rush Order Fee",
            style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
          ),
          content: TextField(
            controller: feeController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: "Enter new fee (\$)",
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("Cancel", style: GoogleFonts.poppins()),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade800,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () {
                setState(() {
                  rushOrderFee = double.tryParse(feeController.text) ?? rushOrderFee;
                });
                Navigator.pop(context);
              },
              child: Text("Save", style: GoogleFonts.poppins()),
            ),
          ],
        );
      },
    );
  }

  void _editRushTime() {
    TextEditingController timeController =
        TextEditingController(text: rushPreparationTime.toString());

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            "Set Rush Preparation Time",
            style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
          ),
          content: TextField(
            controller: timeController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: "Enter time in minutes",
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("Cancel", style: GoogleFonts.poppins()),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade800,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () {
                setState(() {
                  rushPreparationTime =
                      int.tryParse(timeController.text) ?? rushPreparationTime;
                });
                Navigator.pop(context);
              },
              child: Text("Save", style: GoogleFonts.poppins()),
            ),
          ],
        );
      },
    );
  }
}
