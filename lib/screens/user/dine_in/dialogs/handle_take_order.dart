import 'package:easydine_main/blocs/table/table_bloc.dart';
import 'package:easydine_main/blocs/table/table_event.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../dialogs/new_order.dart';
import '../../../../services/order_id_generator.dart';

void handleTakeOrder(BuildContext context, Map<String, dynamic> table) {
  if (table['status'] != 'Occupied') {
    // Get the TableBloc instance
    final tableBloc = context.read<TableBloc>();
    
    // Mark table as occupied first
    tableBloc.add(MarkTableAsOccupied(
      tableId: table['id'],
      customerDetails: {
        'time': DateTime.now().toString(),
        'type': 'Dine-in',
      },
    ));
  }

  // Show the order dialog
  showDialog(
    context: context,
    builder: (context) => ShowOrderDialog(
      tableNumber: table['id'].toString(),
      orderId: OrderIdGenerator.generateOrderId(
        orderType: 'Dine-In',
        tableNumber: table['id'].toString(),
        priority: 2,
      ),
    ),
  );
}
