import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../../blocs/table/table_bloc.dart';
import '../../../../blocs/table/table_event.dart';
import '../../../../blocs/table/table_state.dart';
import '../../running_orders.dart';


void handleViewBill(BuildContext context, Map<String, dynamic> table) {
  final tableBloc = context.read<TableBloc>();
  tableBloc.add(ViewTableBill(tableId: table['tableNumber'].toString()));

  showDialog(
    context: context,
    builder: (context) => BlocBuilder<TableBloc, TableState>(
      builder: (context, state) {
        if (state.error != null) {
          return AlertDialog(
            backgroundColor: Colors.grey[900],
            title: Text(
              'No Active Bill',
              style: GoogleFonts.poppins(color: Colors.white),
            ),
            content: Text(
              state.error!,
              style: GoogleFonts.poppins(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('OK'),
              ),
            ],
          );
        }

        if (state.currentOrder != null) {
          return Dialog(
            backgroundColor: Colors.grey[900],
            child: OrderDetailsModal(order: {
              'id': state.currentOrder!.id,
              'table': state.currentOrder!.tableId,
              'items': state.currentOrder!.items.map((item) => {
                'id': item.id,
                'name': item.name,
                'price': item.price,
                'quantity': item.quantity,
                'customization': item.customization,
              }).toList(),
              'status': state.currentOrder!.status,
              'time': state.currentOrder!.createdAt.toString(),
              'total': state.currentOrder!.total,
            }),
          );
        }

        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    ),
  );
}
