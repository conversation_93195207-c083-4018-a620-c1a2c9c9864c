import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

Widget buildStatItem({
  required IconData icon,
  required String label,
  required String value,
  required Color color,
}) {
  return Column(
    children: [
      Container(
        padding: EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(icon, color: color),
      ),
      SizedBox(height: 8),
      Text(
        label,
        style: GoogleFonts.poppins(
          color: Colors.white60,
          fontSize: 12,
        ),
      ),
      Text(
        value,
        style: GoogleFonts.poppins(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    ],
  );
}