import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class FloorIndicator extends StatelessWidget {
  final int currentFloor;
  final List<int> availableFloors;
  final Function(int) onFloorChanged;

  const FloorIndicator({
    Key? key,
    required this.currentFloor,
    required this.availableFloors,
    required this.onFloorChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white24,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: availableFloors.length,
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        itemBuilder: (context, index) {
          final floor = availableFloors[index];
          final isSelected = floor == currentFloor;

          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => onFloorChanged(floor),
                borderRadius: BorderRadius.circular(8),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.black54 : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected ? Colors.white : Colors.white38,
                      width: 1.5,
                    ),
                  ),
                  child: Center(
                    child: AnimatedDefaultTextStyle(
                      duration: const Duration(milliseconds: 200),
                      style: GoogleFonts.poppins(
                        color: isSelected ? Colors.white : Colors.white70,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        fontSize: isSelected ? 14 : 13,
                      ),
                      child: Text('Floor $floor'),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
