import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../utils/get_table_color.dart';
import '../../../../utils/seat_painter.dart';
import '../../../../utils/table_border_painter.dart';
import '../../../../widgets/cleaning_status_indicator.dart';
import '../../../../models/table_model.dart';

class TableCard extends StatelessWidget {
  final TableModel table;
  final VoidCallback onTap;

  const TableCard({
    super.key,
    required this.table,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final statusColor = getTableColor(table.status);
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: statusColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: statusColor.withOpacity(0.3)),
        ),
        child: AspectRatio(
          aspectRatio: 1,
          child: Stack(
            children: [
              CustomPaint(
                painter: SeatPainter(
                  seatCount: table.seats,
                  color: Color.fromRGBO(207, 207, 207, 1.0),
                ),
                size: Size.infinite,
              ),
              CustomPaint(
                painter: TableBorderPainter(
                  seats: table.seats,
                  color: getTableColor(table.status),
                  strokeWidth: 3.0,
                ),
                size: Size.infinite,
              ),
              Positioned(
                top: 8,
                right: 8,
                child: buildCleaningStatusIndicator(table.cleaningStatus),
              ),
              Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Table ${table.tableNumber}',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        '${table.seats} Seats',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 2),
                        padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          table.status,
                          style: GoogleFonts.poppins(
                            color: statusColor,
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
