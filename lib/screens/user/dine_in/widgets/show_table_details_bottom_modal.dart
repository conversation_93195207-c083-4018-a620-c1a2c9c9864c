// import 'dart:ui';
//
// import 'package:animate_do/animate_do.dart';
// import 'package:easydine_main/screens/user/dine_in/widgets/quick_action_card.dart';
// import 'package:easydine_main/screens/user/dine_in/widgets/stat_item.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:intl/intl.dart';
//
// import '../../../../utils/get_table_color.dart';
// import '../../../../utils/seat_painter.dart';
// import '../../../../utils/table_border_painter.dart';
// import '../dialogs/handle_mark_clean.dart';
// import '../dialogs/handle_take_order.dart';
// import '../dialogs/handle_view_bill.dart';
// import '../dialogs/show_reservation_dialog.dart';
//
// class TableDetailsBottomSheet extends StatefulWidget {
//   final Map<String, dynamic> table;
//   final List<Map<String, dynamic>> allTables;
//
//   const TableDetailsBottomSheet({
//     Key? key,
//     required this.table,
//     required this.allTables,
//   }) : super(key: key);
//
//   @override
//   State<TableDetailsBottomSheet> createState() => _TableDetailsBottomSheetState();
// }
//
// class _TableDetailsBottomSheetState extends State<TableDetailsBottomSheet> {
//   final primaryGreen = Color(0xFF2CBF5A);
//   final accentBlue = Color(0xFF2196F3);
//   final warningAmber = Color(0xFFFFA000);
//   final errorRed = Color(0xFFE53935);
//
//   @override
//   Widget build(BuildContext context) {
//     return BackdropFilter(
//       filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
//       child: Container(
//         height: MediaQuery.of(context).size.height * 0.8,
//         decoration: BoxDecoration(
//           color: Colors.grey[900],
//           borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.all(24.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               // Header - Fixed size container
//               Container(
//                 padding: EdgeInsets.all(16),
//                 height: MediaQuery.of(context).orientation == Orientation.portrait
//                     ? MediaQuery.of(context).size.height * 0.15
//                     : MediaQuery.of(context).size.height * 0.25,
//                 decoration: BoxDecoration(
//                   color: Colors.black26,
//                   borderRadius: BorderRadius.circular(12),
//                 ),
//                 child: Row(
//                   children: [
//                     // Left side with Table ID and visual - Fixed width
//                     SizedBox(
//                       width: MediaQuery.of(context).size.width * 0.35,
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             'Table ${widget.table['id']}',
//                             style: GoogleFonts.poppins(
//                               color: Colors.white,
//                               fontSize: 24,
//                               fontWeight: FontWeight.bold,
//                             ),
//                           ),
//                           Expanded(
//                             child: Container(
//                               margin: EdgeInsets.only(top: 4),
//                               child: Stack(
//                                 fit: StackFit.expand,
//                                 children: [
//                                   CustomPaint(
//                                     painter: SeatPainter(
//                                       seatCount: widget.table['seats'],
//                                       color: Color.fromRGBO(207, 207, 207, 1.0),
//                                     ),
//                                     size: Size.infinite,
//                                   ),
//                                   CustomPaint(
//                                     painter: TableBorderPainter(
//                                       seats: widget.table['seats'],
//                                       color: getTableColor(widget.table['status']),
//                                       strokeWidth: 3.0,
//                                     ),
//                                     size: Size.infinite,
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           )
//                         ],
//                       ),
//                     ),
//
//                     SizedBox(width: 16),
//
//                     // Right side with status indicators - Fixed width
//                     Expanded(
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           Text(
//                             "STATUS",
//                             style: GoogleFonts.poppins(
//                               color: Colors.white60,
//                               fontSize: 12,
//                               fontWeight: FontWeight.w500,
//                               letterSpacing: 0.5,
//                             ),
//                           ),
//                           SizedBox(height: 8),
//                           _buildStatusIndicator(),
//                           Spacer(),
//                           Text(
//                             "CLEANING",
//                             style: GoogleFonts.poppins(
//                               color: Colors.white60,
//                               fontSize: 12,
//                               fontWeight: FontWeight.w500,
//                               letterSpacing: 0.5,
//                             ),
//                           ),
//                           SizedBox(height: 8),
//                           _buildCleaningStatusIndicator(),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//
//               SizedBox(height: MediaQuery.of(context).size.height * 0.02),
//
//               Expanded(
//                 child: SingleChildScrollView(
//                   physics: BouncingScrollPhysics(),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       _buildTableInformation(),
//                       SizedBox(height: 24),
//                       _buildQuickActions(),
//                       if (widget.table['status'] == 'Reserved') ...[
//                         SizedBox(height: 24),
//                         _buildReservationDetails(),
//                       ],
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget _buildStatusIndicator() {
//     return Container(
//       padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
//       decoration: BoxDecoration(
//         color: getTableColor(widget.table['status']).withOpacity(0.15),
//         borderRadius: BorderRadius.circular(12),
//         border: Border.all(
//           color: getTableColor(widget.table['status']).withOpacity(0.3),
//           width: 1,
//         ),
//       ),
//       child: Row(
//         mainAxisSize: MainAxisSize.max,
//         children: [
//           Container(
//             width: 8,
//             height: 8,
//             decoration: BoxDecoration(
//               color: getTableColor(widget.table['status']),
//               shape: BoxShape.circle,
//             ),
//           ),
//           SizedBox(width: 8),
//           Text(
//             widget.table['status'],
//             style: GoogleFonts.poppins(
//               color: getTableColor(widget.table['status']),
//               fontWeight: FontWeight.w600,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildCleaningStatusIndicator() {
//     return Container(
//       padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
//       decoration: BoxDecoration(
//         color: getCleaningStatusColor(widget.table['cleaningStatus']).withOpacity(0.15),
//         borderRadius: BorderRadius.circular(12),
//         border: Border.all(
//           color: getCleaningStatusColor(widget.table['cleaningStatus']).withOpacity(0.3),
//           width: 1,
//         ),
//       ),
//       child: Row(
//         mainAxisSize: MainAxisSize.max,
//         children: [
//           Container(
//             width: 8,
//             height: 8,
//             decoration: BoxDecoration(
//               color: getCleaningStatusColor(widget.table['cleaningStatus']),
//               shape: BoxShape.circle,
//             ),
//           ),
//           SizedBox(width: 8),
//           Text(
//             widget.table['cleaningStatus'],
//             style: GoogleFonts.poppins(
//               color: getCleaningStatusColor(widget.table['cleaningStatus']),
//               fontWeight: FontWeight.w600,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildTableInformation() {
//     return FadeInUp(
//       duration: Duration(milliseconds: 400),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             'Table Information',
//             style: GoogleFonts.poppins(
//               color: Colors.white70,
//               fontSize: 16,
//               fontWeight: FontWeight.w600,
//             ),
//           ),
//           SizedBox(height: 16),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceAround,
//             children: [
//               buildStatItem(
//                 icon: Icons.event_seat,
//                 label: 'Capacity',
//                 value: '${widget.table['seats']} seats',
//                 color: warningAmber,
//               ),
//               buildStatItem(
//                 icon: Icons.location_on,
//                 label: 'Location',
//                 value: widget.table['location'],
//                 color: accentBlue,
//               ),
//               if (widget.table['status'] == 'Reserved')
//                 buildStatItem(
//                   icon: Icons.access_time,
//                   label: 'Reserved For',
//                   value: widget.table['reservationTime'] != null
//                       ? DateFormat('dd-mm-yy hh:mm a').format(widget.table['reservationTime'])
//                       : 'N/A',
//                   color: primaryGreen,
//                 ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildQuickActions() {
//     return FadeInUp(
//       duration: Duration(milliseconds: 600),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             'Quick Actions',
//             style: GoogleFonts.poppins(
//               color: Colors.white70,
//               fontSize: 16,
//               fontWeight: FontWeight.w600,
//             ),
//           ),
//           SizedBox(height: 16),
//           GridView.count(
//             shrinkWrap: true,
//             physics: NeverScrollableScrollPhysics(),
//             crossAxisCount: 2,
//             mainAxisSpacing: 8,
//             crossAxisSpacing: 8,
//             childAspectRatio: 3,
//             children: [
//               buildQuickActionCard(
//                 icon: Icons.restaurant_menu,
//                 label: 'Take Order',
//                 color: primaryGreen,
//                 onTap: () => handleTakeOrder(context, widget.table),
//               ),
//               buildQuickActionCard(
//                 icon: Icons.receipt_long,
//                 label: 'View Bill',
//                 color: accentBlue,
//                 onTap: () => handleViewBill(context, widget.table),
//               ),
//               buildQuickActionCard(
//                 icon: Icons.cleaning_services,
//                 label: 'Mark Clean',
//                 color: warningAmber,
//                 onTap: () => handleMarkClean(context, widget.table),
//               ),
//               buildQuickActionCard(
//                 icon: Icons.event_available,
//                 label: 'Reserve',
//                 color: Colors.purple,
//                 onTap: _handleReservation,
//               ),
//             ],
//           ),
//         ],
//       ),
//     );
//   }
//
//   void handleMarkClean(BuildContext context, Map<String, dynamic> table) {
//     if (table['status'] == 'Occupied') {
//       showDialog(
//         context: context,
//         builder: (context) => AlertDialog(
//           backgroundColor: Colors.grey[900],
//           title: Text(
//             'Table Still Occupied',
//             style: GoogleFonts.poppins(color: Colors.white),
//           ),
//           content: Text(
//             'Please close the table before changing cleaning status.',
//             style: GoogleFonts.poppins(color: Colors.white70),
//           ),
//           actions: [
//             TextButton(
//               onPressed: () => Navigator.pop(context),
//               child: Text('OK'),
//             ),
//           ],
//         ),
//       );
//       return;
//     }
//
//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         backgroundColor: Colors.grey[900],
//         title: Text(
//           'Update Cleaning Status',
//           style: GoogleFonts.poppins(color: Colors.white),
//         ),
//         content: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             buildCleaningStatusButton(
//               context,
//               table,
//               'Clean',
//               Color(0xFF2CBF5A),
//             ),
//             SizedBox(height: 8),
//             buildCleaningStatusButton(
//               context,
//               table,
//               'Needs Cleaning',
//               Color(0xFFFFA000),
//             ),
//             SizedBox(height: 8),
//             buildCleaningStatusButton(
//               context,
//               table,
//               'Being Cleaned',
//               Color(0xFF2196F3),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   void _handleTableStatusChange(
//       BuildContext context, Map<String, dynamic> table, List<Map<String, dynamic>> allTables,
//       List<Map<String, dynamic>> filteredTables
//       ) {
//     setState(() {
//       final index = allTables.indexWhere((t) => t['id'] == table['id']);
//       if (index != -1) {
//         if (table['status'] == 'Available') {
//           allTables[index]['status'] = 'Occupied';
//         } else if (table['status'] == 'Occupied') {
//           allTables[index]['status'] = 'Available';
//         }
//         filteredTables = List.from(allTables);
//         Navigator.pop(context); // Close the details dialog
//       }
//     });
//   }
//
//
//
//
// void showTableDetails(BuildContext context, Map<String, dynamic> table, List<Map<String, dynamic>> allTables) {
//   showModalBottomSheet(
//     context: context,
//     constraints: BoxConstraints(
//       maxHeight: MediaQuery.of(context).size.height * 0.8,
//       maxWidth: MediaQuery.of(context).size.width * 0.75,
//     ),
//     backgroundColor: Colors.transparent,
//     isScrollControlled: true,
//     builder: (context) => TableDetailsBottomSheet(
//       table: table,
//       allTables: allTables,
//     ),
//   );
// }
