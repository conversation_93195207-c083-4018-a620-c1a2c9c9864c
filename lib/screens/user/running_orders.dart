import 'package:easydine_main/widgets/app_bar.dart';
import 'package:easydine_main/widgets/tiled_background.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:ui' as ui;
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/running_orders/running_orders_bloc.dart';
import '../../blocs/running_orders/running_orders_event.dart';
import '../../blocs/running_orders/running_orders_state.dart';

import '../../widgets/running_order_card.dart';

// Import the RunningOrderCard widget
// import 'running_order_card.dart';

class RunningOrdersPage extends StatelessWidget {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  RunningOrdersPage({super.key});

  void _handleOrderTap(Map<String, dynamic> order, BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      constraints: BoxConstraints(
        maxWidth: isLandscape 
          ? MediaQuery.of(context).size.width * 0.85
          : MediaQuery.of(context).size.width * 0.75,
        maxHeight: MediaQuery.of(context).orientation == Orientation.portrait
          ? MediaQuery.of(context).size.height * 0.75
          : MediaQuery.of(context).size.height * 0.9,
      ),
      builder: (context) => SafeArea(
        child: Container(
          margin: isLandscape 
            ? EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 16,
              )
            : null,
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(20),
              bottom: isLandscape ? Radius.circular(20) : Radius.zero,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (!isLandscape) Container(
                width: 40,
                height: 4,
                margin: EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey[600],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              
              if (isLandscape) Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  icon: Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                  padding: EdgeInsets.all(16),
                ),
              ),

              Expanded(
                child: OrderDetailsModal(order: order),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => RunningOrdersBloc()..add(FetchRunningOrders()),
      child: DefaultTabController(
        length: 4, // Number of tabs
        child: Scaffold(
          extendBodyBehindAppBar: true,
          key: _scaffoldKey,
          appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
          body: Stack(
            children: [
              TiledBackground(),
              BackdropFilter(
                filter: ui.ImageFilter.blur(sigmaX: 32, sigmaY: 32),
                child: BlocBuilder<RunningOrdersBloc, RunningOrdersState>(
                  builder: (context, state) {
                    if (state.status == RunningOrdersStatus.loading) {
                      return Center(child: CircularProgressIndicator());
                    }

                    if (state.status == RunningOrdersStatus.failure) {
                      return Center(
                        child: Text(
                          state.error ?? 'An error occurred',
                          style: TextStyle(color: Colors.red),
                        ),
                      );
                    }

                    final filteredOrders = state.orders;

                    return Column(
                      children: [
                        SizedBox(height: AppBar().preferredSize.height + 30),
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(25),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: TabBar(
                            dividerHeight: 0,
                            indicator: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(25),
                            ),
                            labelStyle: GoogleFonts.dmSans(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                            unselectedLabelStyle: GoogleFonts.dmSans(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            labelColor: Colors.white,
                            unselectedLabelColor: Colors.white.withOpacity(0.5),
                            padding: EdgeInsets.all(4),
                            labelPadding: EdgeInsets.symmetric(horizontal: 4),
                            tabs: [
                              _buildTab("All", Icons.list_alt,context),
                              _buildTab("Dine-in", Icons.restaurant,context),
                              _buildTab("Takeaway", Icons.takeout_dining,context),
                              _buildTab("Delivery", Icons.delivery_dining,context),
                            ],
                            onTap: (index) {
                              String category = index == 0
                                  ? "All"
                                  : index == 1
                                      ? "Dine-in"
                                      : index == 2
                                          ? "Takeaway"
                                          : "Delivery";
                              context
                                  .read<RunningOrdersBloc>()
                                  .add(FilterByCategory(category));
                            },
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                        // Stats summary with updated styling
                        Container(
                          margin: EdgeInsets.symmetric(horizontal: 16),
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.1),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildStatItem(
                                "Total",
                                filteredOrders.length.toString(),
                                Colors.blue,
                                Icons.receipt_long,
                              ),
                              _buildStatItem(
                                "Ready",
                                filteredOrders
                                    .where(
                                        (order) => order["status"] == "Ready")
                                    .length
                                    .toString(),
                                Colors.green,
                                Icons.check_circle,
                              ),
                              _buildStatItem(
                                "In Progress",
                                filteredOrders
                                    .where((order) =>
                                        order["status"] == "In Progress")
                                    .length
                                    .toString(),
                                Colors.amber,
                                Icons.pending,
                              ),
                              _buildStatItem(
                                "Cooking",
                                filteredOrders
                                    .where(
                                        (order) => order["status"] == "Cooking")
                                    .length
                                    .toString(),
                                Colors.orange,
                                Icons.restaurant,
                              ),
                            ],
                          ),
                        ),
                          SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                        Expanded(
                          child: filteredOrders.isEmpty
                              ? _buildEmptyState()
                              : state.selectedCategory == "All"
                                  ? _buildGroupedOrdersList(filteredOrders, context)
                                  : _buildOrdersList(filteredOrders, context),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.assignment_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            "No orders found",
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupedOrdersList(List<Map<String, dynamic>> orders, BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    
    // Group orders by type
    final Map<String, List<Map<String, dynamic>>> groupedOrders = {
      'Dine-in': [],
      'Takeaway': [],
      'Delivery': [],
    };
    
    for (var order in orders) {
      final type = order['type'] as String;
      groupedOrders[type]?.add(order);
    }
    
    // Remove empty categories
    groupedOrders.removeWhere((key, value) => value.isEmpty);
    
    if (isLandscape) {
      return ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: groupedOrders.length,
        itemBuilder: (context, index) {
          final category = groupedOrders.keys.elementAt(index);
          final categoryOrders = groupedOrders[category]!;
          
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
                child: Row(
                  children: [
                    Icon(
                      _getCategoryIcon(category),
                      color: Colors.white70,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      category,
                      style: GoogleFonts.dmSans(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(width: 8),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${categoryOrders.length}',
                        style: GoogleFonts.dmSans(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              StaggeredGrid.count(
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                children: categoryOrders.map((order) {
                  return StaggeredGridTile.extent(
                    crossAxisCellCount: 1,
                    mainAxisExtent: MediaQuery.of(context).size.height * 0.23,
                    child: InkWell(
                      onTap: () => _handleOrderTap(order, context),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.08),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.1),
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: BackdropFilter(
                            filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                            child: RunningOrderCard(order: order),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          );
        },
      );
    }
    
    // Portrait mode with grouped orders
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: groupedOrders.length,
      itemBuilder: (context, index) {
        final category = groupedOrders.keys.elementAt(index);
        final categoryOrders = groupedOrders[category]!;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
              child: Row(
                children: [
                  Icon(
                    _getCategoryIcon(category),
                    color: Colors.white70,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    category,
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(width: 8),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${categoryOrders.length}',
                      style: GoogleFonts.dmSans(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            ...categoryOrders.map((order) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: InkWell(
                  onTap: () => _handleOrderTap(order, context),
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.1),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: BackdropFilter(
                        filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                        child: RunningOrderCard(order: order),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Dine-in':
        return Icons.restaurant;
      case 'Takeaway':
        return Icons.takeout_dining;
      case 'Delivery':
        return Icons.delivery_dining;
      default:
        return Icons.list_alt;
    }
  }

  Widget _buildOrdersList(
      List<Map<String, dynamic>> orders, BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    if (isLandscape) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: StaggeredGrid.count(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          children: orders.map((order) {
            return StaggeredGridTile.extent(
              crossAxisCellCount: 1,
              mainAxisExtent: MediaQuery.of(context).size.height * 0.23,
              child: InkWell(
                onTap: () => _handleOrderTap(order,context),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.08),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.1),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: BackdropFilter(
                      filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                      child: RunningOrderCard(order: order),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      );
    }

    // Portrait mode - keep the original list view
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: InkWell(
            onTap: () => _handleOrderTap(order,context),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.08),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: BackdropFilter(
                  filter: ui.ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: RunningOrderCard(order: order),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTab(String text, IconData icon, BuildContext context) {
    return Tab(
      height: MediaQuery.of(context).orientation == Orientation.portrait
          ? 48
          : 36,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 18),
            SizedBox(width: 8),
            Text(text),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String label, String value, Color color, IconData icon) {
    return Row(
      children: [
        Container(
          margin: EdgeInsets.only(right: 16),
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: GoogleFonts.dmSans(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              label,
              style: GoogleFonts.dmSans(
                fontSize: 12,
                color: Colors.white.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Order Details Modal
class OrderDetailsModal extends StatefulWidget {
  final Map<String, dynamic> order;
  const OrderDetailsModal({super.key, required this.order});

  @override
  State<OrderDetailsModal> createState() => _OrderDetailsModalState();
}

class _OrderDetailsModalState extends State<OrderDetailsModal> {
  String? selectedPaymentMethod;
  double tipAmount = 0.0;
  final tipController = TextEditingController();
  bool isProcessingPayment = false;
  double cashAmount = 0.0;
  double cardAmount = 0.0;
  bool isSplittingBill = false;
  double firstBillAmount = 0.0;
  double secondBillAmount = 0.0;
  Map<String, int> billAssignments = {}; // 0 for unassigned, 1 for first bill, 2 for second bill
  double firstBillSubtotal = 0.0;
  double secondBillSubtotal = 0.0;

  void _updateBillTotals() {
    firstBillSubtotal = 0.0;
    secondBillSubtotal = 0.0;

    (widget.order['items'] as List).forEach((item) {
      final itemTotal = (item['price'] as double) * (item['quantity'] as int);
      switch (billAssignments[item['id']]) {
        case 1:
          firstBillSubtotal += itemTotal;
          break;
        case 2:
          secondBillSubtotal += itemTotal;
          break;
      }
    });

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final subtotal = (widget.order['items'] as List).fold(0.0, (sum, item) {
      final price = item['price'] ?? 0.0;
      final quantity = item['quantity'] ?? 1;
      return sum + (price * quantity);
    });
    final tax = subtotal * 0.1;
    final total = subtotal + tax + tipAmount;

    if (isLandscape) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Left side - Order details and items
          Expanded(
            flex: 3,
            child: CustomScrollView(
              slivers: [
                SliverPadding(
                  padding: EdgeInsets.all(20),
                  sliver: SliverToBoxAdapter(
                    child: _buildOrderHeader(),
                  ),
                ),
                SliverPadding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  sliver: SliverToBoxAdapter(
                    child: _buildItemsList(),
                  ),
                ),
                SliverPadding(
                  padding: EdgeInsets.only(bottom: 20),
                ),
              ],
            ),
          ),
          
          // Vertical divider
          Container(
            width: 1,
            margin: EdgeInsets.symmetric(vertical: 20),
            color: Colors.grey[800],
          ),
          
          // Right side - Payment and actions
          Expanded(
            flex: 2,
            child: CustomScrollView(
              slivers: [
                SliverPadding(
                  padding: EdgeInsets.all(20),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      _buildPaymentSection(),
                      SizedBox(height: 24),
                      _buildTipSection(subtotal),
                      SizedBox(height: 24),
                      _buildBillSummary(subtotal, tax, total),
                      SizedBox(height: 24),
                      _buildActionButtons(total),
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    // Portrait layout
    return CustomScrollView(
      slivers: [
        SliverPadding(
          padding: EdgeInsets.all(20),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              _buildOrderHeader(),
              SizedBox(height: 24),
              _buildItemsList(),
              SizedBox(height: 24),
              _buildPaymentSection(),
              SizedBox(height: 24),
              _buildTipSection(subtotal),
              SizedBox(height: 24),
              _buildBillSummary(subtotal, tax, total),
              SizedBox(height: 24),
              _buildActionButtons(total),
              // Add bottom padding for safe area
              SizedBox(height: MediaQuery.of(context).padding.bottom),
            ]),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Order #${widget.order['id']}",
              style: GoogleFonts.dmSans(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Text(
              "${widget.order['type']} • Table ${widget.order['table']}",
              style: GoogleFonts.dmSans(
                color: Colors.grey[400],
                fontSize: 16,
              ),
            ),
          ],
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: _getStatusColor(widget.order['status']),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            widget.order['status'],
            style: GoogleFonts.dmSans(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isSplittingBill) ...[
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Row(
              children: [
                Text(
                  'Split Bills',
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Spacer(),
                TextButton.icon(
                  icon: Icon(Icons.close, color: Colors.red),
                  label: Text(
                    'Cancel Split',
                    style: GoogleFonts.dmSans(color: Colors.red),
                  ),
                  onPressed: () {
                    setState(() {
                      isSplittingBill = false;
                      billAssignments.clear();
                      _updateBillTotals();
                    });
                  },
                ),
              ],
            ),
          ),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withOpacity(0.5)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bill 1',
                        style: GoogleFonts.dmSans(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '\$${firstBillSubtotal.toStringAsFixed(2)}',
                        style: GoogleFonts.dmSans(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.withOpacity(0.5)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bill 2',
                        style: GoogleFonts.dmSans(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '\$${secondBillSubtotal.toStringAsFixed(2)}',
                        style: GoogleFonts.dmSans(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
        ],
        ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.4,
          ),
          child: ListView.builder(
            shrinkWrap: true,
            physics: AlwaysScrollableScrollPhysics(),
            itemCount: (widget.order['items'] as List).length,
            itemBuilder: (context, index) {
              final item = widget.order['items'][index];
              final itemId = item['id'] as String;
              final billAssignment = billAssignments[itemId] ?? 0;

              return Container(
                decoration: BoxDecoration(
                  color: billAssignment == 1
                      ? Colors.blue.withOpacity(0.1)
                      : billAssignment == 2
                          ? Colors.green.withOpacity(0.1)
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListTile(
                  contentPadding: EdgeInsets.symmetric(horizontal: 12),
                  title: Text(
                    item['name'],
                    style: GoogleFonts.dmSans(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Text(
                    'Quantity: ${item['quantity']} • \$${(item['price'] * item['quantity']).toStringAsFixed(2)}',
                    style: GoogleFonts.dmSans(
                      color: Colors.grey[400],
                    ),
                  ),
                  trailing: isSplittingBill
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _buildBillCheckbox(
                              itemId,
                              1,
                              Colors.blue,
                              'Bill 1',
                            ),
                            SizedBox(width: 8),
                            _buildBillCheckbox(
                              itemId,
                              2,
                              Colors.green,
                              'Bill 2',
                            ),
                          ],
                        )
                      : Text(
                          '\$${(item['price'] * item['quantity']).toStringAsFixed(2)}',
                          style: GoogleFonts.dmSans(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBillCheckbox(String itemId, int billNumber, Color color, String tooltip) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: () {
          setState(() {
            if (billAssignments[itemId] == billNumber) {
              billAssignments[itemId] = 0;
            } else {
              billAssignments[itemId] = billNumber;
            }
            _updateBillTotals();
          });
        },
        child: Container(
          padding: EdgeInsets.all(4),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: billAssignments[itemId] == billNumber
                ? color
                : Colors.grey.withOpacity(0.2),
            border: Border.all(
              color: color.withOpacity(0.5),
              width: 2,
            ),
          ),
          child: billAssignments[itemId] == billNumber
              ? Icon(Icons.check, size: 16, color: Colors.white)
              : SizedBox(width: 16, height: 16),
        ),
      ),
    );
  }

  Widget _buildPaymentSection() {
    double calculateTotal() {
      final subtotal = (widget.order['items'] as List).fold(0.0, (sum, item) {
        final price = (item['price'] ?? 0.0) as double;
        final quantity = (item['quantity'] ?? 1) as int;
        return sum + (price * quantity);
      });

      final tax = subtotal * 0.1; // 10% tax
      final total =
          subtotal + tax + tipAmount; // tipAmount should be a class variable

      return total;
    }

    final total = calculateTotal();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildPaymentOption(
                'Cash',
                Icons.payments_outlined,
                Colors.green,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildPaymentOption(
                'Card',
                Icons.credit_card_outlined,
                Colors.blue,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: _buildPaymentOption(
                'Split',
                Icons.call_split_outlined,
                Colors.orange,
              ),
            ),
          ],
        ),
        if (selectedPaymentMethod == 'Split') ...[
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Cash Amount',
                    labelStyle: TextStyle(color: Colors.white70),
                    prefixIcon:
                        Icon(Icons.payments_outlined, color: Colors.green),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white24),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.green),
                    ),
                  ),
                  style: TextStyle(color: Colors.white),
                  onChanged: (value) {
                    setState(() {
                      cashAmount = double.tryParse(value) ?? 0;
                      cardAmount = total - cashAmount;
                    });
                  },
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: TextField(
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Card Amount',
                    labelStyle: TextStyle(color: Colors.white70),
                    prefixIcon:
                        Icon(Icons.credit_card_outlined, color: Colors.blue),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.white24),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.blue),
                    ),
                  ),
                  style: TextStyle(color: Colors.white),
                  onChanged: (value) {
                    setState(() {
                      cardAmount = double.tryParse(value) ?? 0;
                      cashAmount = total - cardAmount;
                    });
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Split Amount:',
                style: GoogleFonts.dmSans(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
              Text(
                '\$${(cashAmount + cardAmount).toStringAsFixed(2)} / \$${total.toStringAsFixed(2)}',
                style: GoogleFonts.dmSans(
                  color: (cashAmount + cardAmount) == total
                      ? Colors.green
                      : Colors.orange,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildTipSection(double subtotal) {
    return Column(
      children: [
        Text(
          "Add Tip",
          style: GoogleFonts.dmSans(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        SizedBox(height: 12),
        Row(
          children: [
            _buildTipButton('10%', subtotal * 0.1),
            SizedBox(width: 8),
            _buildTipButton('15%', subtotal * 0.15),
            SizedBox(width: 8),
            _buildTipButton('20%', subtotal * 0.2),
            SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: tipController,
                keyboardType: TextInputType.number,
                style: GoogleFonts.dmSans(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Custom',
                  hintStyle: GoogleFonts.dmSans(color: Colors.grey),
                  prefixText: '\$ ',
                  prefixStyle:
                      GoogleFonts.dmSans(color: Colors.white),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[700]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.white),
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    tipAmount = double.tryParse(value) ?? 0;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBillSummary(double subtotal, double tax, double total) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildBillRow('Subtotal', subtotal),
          SizedBox(height: 8),
          _buildBillRow('Tax (10%)', tax),
          SizedBox(height: 8),
          _buildBillRow('Tip', tipAmount),
          Divider(color: Colors.grey[600], height: 24),
          _buildBillRow('Total', total, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildActionButtons(double total) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        border: Border(top: BorderSide(color: Colors.grey[800]!)),
      ),
      child: Row(
        children: [
          if (!isSplittingBill) ...[
            Expanded(
              child: ElevatedButton.icon(
                icon: Icon(Icons.call_split, color: Colors.white),
                label: Text(
                  "Split Bill",
                  style: GoogleFonts.dmSans(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: () {
                  setState(() {
                    isSplittingBill = true;
                  });
                },
              ),
            ),
            SizedBox(width: 12),
          ],
          Expanded(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[700],
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () => Navigator.pop(context),
              child: Text(
                "Cancel",
                style: GoogleFonts.dmSans(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF2CBF5A),
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: isSplittingBill && billAssignments.isEmpty
                  ? null
                  : () => _processPayment(context, total),
              child: Text(
                isSplittingBill ? "Continue Split Payment" : "Process Payment",
                style: GoogleFonts.dmSans(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOption(String title, IconData icon, Color color) {
    final isSelected = selectedPaymentMethod == title;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedPaymentMethod = title;
          if (title == 'Split') {
            _showSplitAmountDialog();
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.grey[800],
          border: Border.all(
            color: isSelected ? color : Colors.transparent,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey[400],
              size: 28,
            ),
            SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.dmSans(
                color: isSelected ? color : Colors.grey[400],
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipButton(String label, double amount) {
    final isSelected = tipAmount == amount;
    return GestureDetector(
      onTap: () {
        setState(() {
          tipAmount = amount;
          tipController.text = amount.toStringAsFixed(2);
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Color(0xFF2CBF5A).withOpacity(0.2)
              : Colors.grey[800],
          border: Border.all(
            color: isSelected ? Color(0xFF2CBF5A) : Colors.transparent,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          label,
          style: GoogleFonts.dmSans(
            color: isSelected ? Color(0xFF2CBF5A) : Colors.grey[400],
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildBillRow(String label, double amount, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.dmSans(
            color: Colors.grey[400],
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          '\$${amount.toStringAsFixed(2)}',
          style: GoogleFonts.dmSans(
            color: isTotal ? Colors.white : Colors.grey[400],
            fontSize: isTotal ? 18 : 16,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  // Widget _buildPaymentSection() {
  //   double calculateTotal() {
  //     final subtotal = (widget.order['items'] as List).fold(0.0, (sum, item) {
  //       final price = (item['price'] ?? 0.0) as double;
  //       final quantity = (item['quantity'] ?? 1) as int;
  //       return sum + (price * quantity);
  //     });
  //
  //     final tax = subtotal * 0.1; // 10% tax
  //     final total =
  //         subtotal + tax + tipAmount; // tipAmount should be a class variable
  //
  //     return total;
  //   }
  //
  //   final total = calculateTotal();
  //
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.stretch,
  //     children: [
  //       Row(
  //         children: [
  //           Expanded(
  //             child: _buildPaymentOption(
  //               'Cash',
  //               Icons.payments_outlined,
  //               Colors.green,
  //             ),
  //           ),
  //           SizedBox(width: 12),
  //           Expanded(
  //             child: _buildPaymentOption(
  //               'Card',
  //               Icons.credit_card_outlined,
  //               Colors.blue,
  //             ),
  //           ),
  //           SizedBox(width: 12),
  //           Expanded(
  //             child: _buildPaymentOption(
  //               'Split',
  //               Icons.call_split_outlined,
  //               Colors.orange,
  //             ),
  //           ),
  //         ],
  //       ),
  //       if (selectedPaymentMethod == 'Split') ...[
  //         SizedBox(height: 16),
  //         Row(
  //           children: [
  //             Expanded(
  //               child: TextField(
  //                 keyboardType: TextInputType.number,
  //                 decoration: InputDecoration(
  //                   labelText: 'Cash Amount',
  //                   labelStyle: TextStyle(color: Colors.white70),
  //                   prefixIcon:
  //                       Icon(Icons.payments_outlined, color: Colors.green),
  //                   enabledBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.white24),
  //                   ),
  //                   focusedBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.green),
  //                   ),
  //                 ),
  //                 style: TextStyle(color: Colors.white),
  //                 onChanged: (value) {
  //                   setState(() {
  //                     cashAmount = double.tryParse(value) ?? 0;
  //                     cardAmount = total - cashAmount;
  //                   });
  //                 },
  //               ),
  //             ),
  //             SizedBox(width: 12),
  //             Expanded(
  //               child: TextField(
  //                 keyboardType: TextInputType.number,
  //                 decoration: InputDecoration(
  //                   labelText: 'Card Amount',
  //                   labelStyle: TextStyle(color: Colors.white70),
  //                   prefixIcon:
  //                       Icon(Icons.credit_card_outlined, color: Colors.blue),
  //                   enabledBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.white24),
  //                   ),
  //                   focusedBorder: OutlineInputBorder(
  //                     borderSide: BorderSide(color: Colors.blue),
  //                   ),
  //                 ),
  //                 style: TextStyle(color: Colors.white),
  //                 onChanged: (value) {
  //                   setState(() {
  //                     cardAmount = double.tryParse(value) ?? 0;
  //                     cashAmount = total - cardAmount;
  //                   });
  //                 },
  //               ),
  //             ),
  //           ],
  //         ),
  //         SizedBox(height: 8),
  //         Row(
  //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //           children: [
  //             Text(
  //               'Total Split Amount:',
  //               style: GoogleFonts.dmSans(
  //                 color: Colors.white70,
  //                 fontSize: 14,
  //               ),
  //             ),
  //             Text(
  //               '\$${(cashAmount + cardAmount).toStringAsFixed(2)} / \$${total.toStringAsFixed(2)}',
  //               style: GoogleFonts.dmSans(
  //                 color: (cashAmount + cardAmount) == total
  //                     ? Colors.green
  //                     : Colors.orange,
  //                 fontSize: 14,
  //                 fontWeight: FontWeight.bold,
  //               ),
  //             ),
  //           ],
  //         ),
  //       ],
  //     ],
  //   );
  // }

  Future<void> _processPayment(BuildContext context, double total) async {
    if (selectedPaymentMethod == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please select a payment method')),
      );
      return;
    }

    setState(() {
      isProcessingPayment = true;
    });

    try {
      await Future.delayed(Duration(seconds: 2));

      Map<String, dynamic> paymentDetails = {
        'method': selectedPaymentMethod,
        'total': total,
        'timestamp': DateTime.now().toIso8601String(),
      };

      if (isSplittingBill) {
        paymentDetails['splitBills'] = {
          'firstBill': firstBillAmount,
          'secondBill': secondBillAmount,
        };
      } else if (selectedPaymentMethod == 'Split') {
        paymentDetails['cashAmount'] = cashAmount;
        paymentDetails['cardAmount'] = cardAmount;
      }

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Color(0xFF2CBF5A)),
              SizedBox(width: 8),
              Text(
                'Payment Successful',
                style: GoogleFonts.dmSans(color: Colors.white),
              ),
            ],
          ),
          content: Text(
            isSplittingBill
                ? 'Payment processed successfully!\nFirst Bill: \$${firstBillAmount.toStringAsFixed(2)}\nSecond Bill: \$${secondBillAmount.toStringAsFixed(2)}'
                : selectedPaymentMethod == 'Split'
                    ? 'Payment processed successfully!\nCash: \$${cashAmount.toStringAsFixed(2)}\nCard: \$${cardAmount.toStringAsFixed(2)}'
                    : 'Payment processed successfully!',
            style: GoogleFonts.dmSans(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                GoRouter.of(context).pop();
              },
              child: Text(
                'Done',
                style: GoogleFonts.dmSans(
                  color: Color(0xFF2CBF5A),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      );
    } finally {
      setState(() {
        isProcessingPayment = false;
      });
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'ready':
        return Color(0xFF2CBF5A);
      case 'cooking':
        return Colors.orange;
      case 'in progress':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _showSplitAmountDialog() {
    final TextEditingController cashController = TextEditingController();
    final TextEditingController cardController = TextEditingController();
    double calculateTotal() {
      final subtotal = (widget.order['items'] as List).fold(0.0, (sum, item) {
        final price = (item['price'] ?? 0.0) as double;
        final quantity = (item['quantity'] ?? 1) as int;
        return sum + (price * quantity);
      });

      final tax = subtotal * 0.1; // 10% tax
      final total =
          subtotal + tax + tipAmount; // tipAmount should be a class variable

      return total;
    }

    final double total =
        calculateTotal(); // Your existing total calculation method

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Split Payment',
          style: GoogleFonts.dmSans(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Total Amount: \$${total.toStringAsFixed(2)}',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
            SizedBox(height: 16),
            TextField(
              controller: cashController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Cash Amount',
                labelStyle: TextStyle(color: Colors.white70),
                prefixIcon: Icon(Icons.payments_outlined, color: Colors.green),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white24),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.green),
                ),
              ),
              style: TextStyle(color: Colors.white),
              onChanged: (value) {
                double cash = double.tryParse(value) ?? 0;
                cardController.text = (total - cash).toStringAsFixed(2);
              },
            ),
            SizedBox(height: 12),
            TextField(
              controller: cardController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'Card Amount',
                labelStyle: TextStyle(color: Colors.white70),
                prefixIcon:
                    Icon(Icons.credit_card_outlined, color: Colors.blue),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white24),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
              style: TextStyle(color: Colors.white),
              onChanged: (value) {
                double card = double.tryParse(value) ?? 0;
                cashController.text = (total - card).toStringAsFixed(2);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.dmSans(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
            ),
            onPressed: () {
              setState(() {
                cashAmount = double.tryParse(cashController.text) ?? 0;
                cardAmount = double.tryParse(cardController.text) ?? 0;
              });
              Navigator.pop(context);
            },
            child: Text(
              'Confirm',
              style: GoogleFonts.dmSans(),
            ),
          ),
        ],
      ),
    );
  }
}

// Filter Options Modal
class FilterOptionsModal extends StatefulWidget {
  final Function(Map<String, dynamic>) onApply;

  const FilterOptionsModal({super.key, required this.onApply});

  @override
  State<FilterOptionsModal> createState() => _FilterOptionsModalState();
}

class _FilterOptionsModalState extends State<FilterOptionsModal> {
  List<String> selectedStatuses = [];
  String? sortBy;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Filter Orders",
            style: GoogleFonts.dmSans(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Text(
            "Status",
            style: GoogleFonts.dmSans(
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip("Ready", "Ready"),
              _buildFilterChip("In Progress", "In Progress"),
              _buildFilterChip("Cooking", "Cooking"),
            ],
          ),
          SizedBox(height: 16),
          Text(
            "Sort by",
            style: GoogleFonts.dmSans(
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          Wrap(
            spacing: 8,
            children: [
              _buildSortChip("Newest First", "newest"),
              _buildSortChip("Oldest First", "oldest"),
              _buildSortChip("Table Number", "table"),
            ],
          ),
          SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text("Cancel"),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    padding: EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    widget.onApply({
                      'statuses': selectedStatuses,
                      'sortBy': sortBy,
                    });
                    Navigator.pop(context);
                  },
                  child: Text("Apply"),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = selectedStatuses.contains(value);
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.dmSans(
          color: isSelected ? Colors.white : Colors.black,
        ),
      ),
      selected: isSelected,
      backgroundColor: Colors.grey[200],
      selectedColor: Colors.blue,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            selectedStatuses.add(value);
          } else {
            selectedStatuses.remove(value);
          }
        });
      },
    );
  }

  Widget _buildSortChip(String label, String value) {
    final isSelected = sortBy == value;
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.dmSans(
          color: isSelected ? Colors.white : Colors.black,
        ),
      ),
      selected: isSelected,
      backgroundColor: Colors.grey[200],
      selectedColor: Colors.green,
      onSelected: (selected) {
        setState(() {
          sortBy = selected ? value : null;
        });
      },
    );
  }
}

// Add a new PaymentModal widget for handling individual bill payments
class PaymentModal extends StatefulWidget {
  final double total;
  final String billLabel;

  const PaymentModal({
    required this.total,
    required this.billLabel,
    Key? key,
  }) : super(key: key);

  @override
  _PaymentModalState createState() => _PaymentModalState();
}

class _PaymentModalState extends State<PaymentModal> {
  String? selectedPaymentMethod;
  
  @override
  Widget build(BuildContext context) {
    throw UnimplementedError();
  }
  
  // Implement the payment modal UI here...
}
