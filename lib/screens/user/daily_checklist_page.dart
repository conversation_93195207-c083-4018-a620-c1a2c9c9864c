import 'package:easydine_main/widgets/tiled_background.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import '../../blocs/checklist/signature_bloc.dart';
import '../../blocs/checklist/signature_event.dart';
import '../../blocs/checklist/signature_state.dart';
import '../../models/checklistItem.dart';
import '../../router/router_constants.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/pdf_service.dart';
import '../../models/checklist_report.dart';
import '../../utils/signature_pad_dialog.dart';

class DailyChecklistPage extends StatefulWidget {
  const DailyChecklistPage({super.key});

  @override
  State<DailyChecklistPage> createState() => _DailyChecklistPageState();
}

class _DailyChecklistPageState extends State<DailyChecklistPage> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isLoading = true; // Add loading state

  final List<ChecklistItem> _items = [
    ChecklistItem(
      title: 'Opening Checks',
      subtitle: 'Verify all opening tasks are completed',
      icon: Icons.store,
      subitems: [
        'Check front door is unlocked',
        'Turn on all lights',
        'Check POS system is online',
        'Count float in register',
        'Ensure staff areas are clean'
      ],
    ),
    ChecklistItem(
      title: 'Chilled Storage Checks',
      subtitle: 'Check all chilled storage units',
      icon: Icons.inventory_2,
      subitems: [
        'Fridge 1: Temp below 5°C',
        'Fridge 2: Temp below 5°C',
        'Walk-in cooler: Temp below 5°C',
        'Check for expired items',
        'Ensure proper food storage hierarchy'
      ],
    ),
    ChecklistItem(
      title: 'Cooking Temperature Checks',
      subtitle: 'Check all cooking temperatures',
      icon: Icons.local_fire_department,
      subitems: [
        'Grill: 200°C minimum',
        'Fryer oil: 180°C',
        'Oven: 180°C minimum',
        'Check probe thermometer calibration',
        'Record temperatures in log'
      ],
    ),
    ChecklistItem(
      title: 'Cooling Checks',
      subtitle: 'Complete all cooling checks',
      icon: Icons.ac_unit,
      subitems: [
        'Hot food cooled within 2 hours',
        'Food stored in shallow containers',
        'Proper air circulation around food',
        'Food covered properly',
        'Cooling log completed'
      ],
    ),
    ChecklistItem(
      title: 'Reheating Temperature Checks',
      subtitle: 'Complete all reheating checks',
      icon: Icons.whatshot,
      subitems: [
        'Food reheated to 75°C minimum',
        'Record temperatures in log',
        'Use probe thermometer correctly',
        'Reheat food only once',
        'Discard food if not hot enough'
      ],
    ),
  ];

  // Check if all tasks and signatures are completed
  bool get _allTasksCompleted {
    // First check if all items are completed
    if (!_items.every((item) => item.isCompleted)) {
      return false;
    }
    
    // Next check if all items have signatures
    for (var item in _items) {
      if (!_isItemSigned(item.title)) {
        return false;
      }
    }
    
    return true;
  }

  // Check if an item is signed
  bool _isItemSigned(String itemTitle) {
    final signaturePoints = context.read<SignatureBloc>().state.getSignaturePoints(itemTitle);
    return signaturePoints.isNotEmpty;
  }

  // Helper method to check if a subitem is completed
  bool _isSubitemCompleted(String itemTitle, String subitem) {
    final prefs = SharedPreferences.getInstance().then((prefs) {
      // Try string value first
      final stringValue = prefs.getString('subitem_${itemTitle}_$subitem');
      if (stringValue != null) {
        return stringValue == 'true';
      } else {
        try {
          return prefs.getBool('subitem_${itemTitle}_$subitem') ?? false;
        } catch (e) {
          return false;
        }
      }
    });
    
    // Default to false while waiting for the future
    return false;
  }

  // Store progress in state
  double _overallProgress = 0.0;

  // Update progress calculation
  Future<void> _updateProgress() async {
    double totalSubitems = 0;
    double completedSubitems = 0;
    final prefs = await SharedPreferences.getInstance();
    
    for (var item in _items) {
      if (item.subitems != null) {
        totalSubitems += item.subitems!.length;
        
        // Count completed subitems
        for (var subitem in item.subitems!) {
          // Try string value first
          final stringValue = prefs.getString('subitem_${item.title}_$subitem');
          bool isCompleted = false;
          
          if (stringValue != null) {
            isCompleted = stringValue == 'true';
          } else {
            try {
              isCompleted = prefs.getBool('subitem_${item.title}_$subitem') ?? false;
            } catch (e) {
              isCompleted = false;
            }
          }
          
          if (isCompleted) {
            completedSubitems += 1;
          }
        }
      }
    }
    
    setState(() {
      _overallProgress = totalSubitems > 0 ? completedSubitems / totalSubitems : 0.0;
    });
  }

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _loadChecklist().then((_) {
      _updateProgress().then((_) {
        setState(() {
          _isLoading = false;
        });
        _animationController.forward();
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadChecklist() async {
    final prefs = await SharedPreferences.getInstance();
    final lastCheckDate = prefs.getString('last_check_date');
    final today = DateTime.now().toIso8601String().split('T')[0];
    final wasSkipped = prefs.getBool('checklist_skipped') ?? false;

    if (lastCheckDate != today || (wasSkipped && !(prefs.getBool('checklist_completed') ?? false))) {
      // Reset checklist if it's a new day or was skipped but not completed
      if (lastCheckDate != today) {
        await prefs.setString('last_check_date', today);
        
        // Clear previous signatures for a new day
        for (var item in _items) {
          context.read<SignatureBloc>().add(ClearSignature(item.title));
        }
      }
      
      // Load saved states for items and subitems
      for (var item in _items) {
        item.isCompleted = false;
        
        // Load saved subitems completion states
        if (item.subitems != null) {
          final allSubitemsCompleted = await _areAllSubitemsCompleted(item);
          item.isCompleted = allSubitemsCompleted;
        }
      }
    } else {
      // Load previous session state
      for (var item in _items) {
        final allSubitemsCompleted = await _areAllSubitemsCompleted(item);
        final hasSignature = _isItemSigned(item.title);
        
        // An item is only considered complete if all subitems are done AND it has a signature
        item.isCompleted = allSubitemsCompleted && hasSignature;
      }
    }
  }

  Future<void> _saveChecklistItem(ChecklistItem item) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('checklist_${item.title}', item.isCompleted);
  }

  Future<bool> _areAllSubitemsCompleted(ChecklistItem item) async {
    if (item.subitems == null || item.subitems!.isEmpty) return false;

    final prefs = await SharedPreferences.getInstance();
    
    for (var subitem in item.subitems!) {
      // Check for string value first
      final stringValue = prefs.getString('subitem_${item.title}_$subitem');
      if (stringValue != null) {
        // If it's not "true", the item is not completed
        if (stringValue != 'true') return false;
      } else {
        // Fallback to boolean for backward compatibility
        final isCompleted = prefs.getBool('subitem_${item.title}_$subitem') ?? false;
        if (!isCompleted) return false;
      }
    }

    return true;
  }

  void _proceedToHome() async {
    if (_allTasksCompleted) {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      // Save checklist completion status
      await prefs.setString('last_check_date', today);
      await prefs.setBool('checklist_skipped', false);
      await prefs.setBool('checklist_completed', true);

      // Create checklist report
      final report = ChecklistReport(
        date: today,
        checklistItems: Map.fromEntries(
          await Future.wait(
            _items.map((item) async {
              final subitems = item.subitems ?? [];
              final subitemStatus = Map.fromEntries(
                await Future.wait(
                  subitems.map((subitem) async {
                    // Try to get string value first
                    final stringValue = prefs.getString('subitem_${item.title}_$subitem');
                    final status = stringValue == 'true' || 
                                  (stringValue == null && (prefs.getBool('subitem_${item.title}_$subitem') ?? false));
                    return MapEntry(subitem, status);
                  }),
                ),
              );
              return MapEntry(item.title, Map<String, bool>.from(subitemStatus));
            }),
          ),
        ),
        signatures: Map.from(context.read<SignatureBloc>().state.signatures),
        completedBy: 'Staff Name', // Replace with actual staff name
        completionTime: DateTime.now(),
      );

      // Generate and save PDF
      try {
        final file = await PDFService.generateChecklistReport(report);
        
        // Get existing reports
        final reports = prefs.getStringList('checklist_reports') ?? [];
        
        // Check if a report for today already exists and remove it
        final todayFileName = 'checklist_$today.pdf';
        reports.removeWhere((path) => path.endsWith(todayFileName));
        
        // Add the new report
        reports.add(file.path);
        await prefs.setStringList('checklist_reports', reports);
      } catch (e) {
        print('Error generating PDF: $e');
      }

      if (mounted) {
        GoRouter.of(context).goNamed(RouterConstants.home);
      }
    }
  }

  // Skip button handler
  void _skipChecklist() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('checklist_skipped', true);
    await prefs.setBool('checklist_completed', false);
    
    if (mounted) {
      GoRouter.of(context).goNamed(RouterConstants.home);
    }
  }

  void _openChecklistDetail(ChecklistItem item) async {
    final prefs = await SharedPreferences.getInstance();
    Map<String, bool> subitemStatus = {};

    // Load saved subitem statuses
    if (item.subitems != null) {
      for (var subitem in item.subitems!) {
        // Try to get string value first
        final stringValue = prefs.getString('subitem_${item.title}_$subitem');
        if (stringValue != null) {
          // Convert string value to boolean
          subitemStatus[subitem] = stringValue == 'true';
        } else {
          // Fallback to boolean for backward compatibility
          try {
            subitemStatus[subitem] = prefs.getBool('subitem_${item.title}_$subitem') ?? false;
          } catch (e) {
            // If there's an error (like type mismatch), default to false
            subitemStatus[subitem] = false;
          }
        }
      }
    }

    bool? result = await showDialog<bool>(
      context: context,
      builder: (context) => ChecklistDetailDialog(
        item: item,
        subitemStatus: subitemStatus,
      ),
    );

    if (result == true) {
      // Update the checklist item status
      bool allSubitemsCompleted = await _areAllSubitemsCompleted(item);
      bool hasSignature = _isItemSigned(item.title);
      
      setState(() {
        // Only mark as completed if all subitems are done AND has a signature
        item.isCompleted = allSubitemsCompleted && hasSignature;
        _saveChecklistItem(item);
      });
      
      // Update the progress
      await _updateProgress();
    }
  }

  // Calculate real progress based on completed subitems and signatures
  Future<double> _calculateRealProgress() async {
    double totalSubitems = 0;
    double completedSubitems = 0;
    final prefs = await SharedPreferences.getInstance();
    
    for (var item in _items) {
      if (item.subitems != null) {
        totalSubitems += item.subitems!.length;
        
        // Count completed subitems
        for (var subitem in item.subitems!) {
          // Try string value first
          final stringValue = prefs.getString('subitem_${item.title}_$subitem');
          bool isCompleted = false;
          
          if (stringValue != null) {
            isCompleted = stringValue == 'true';
          } else {
            try {
              isCompleted = prefs.getBool('subitem_${item.title}_$subitem') ?? false;
            } catch (e) {
              isCompleted = false;
            }
          }
          
          if (isCompleted) {
            completedSubitems += 1;
          }
        }
      }
    }
    
    if (totalSubitems == 0) return 0.0;
    return completedSubitems / totalSubitems;
  }

  // Collect all failed items across all checklists
  Future<Map<String, List<String>>> _collectFailedItems() async {
    final prefs = await SharedPreferences.getInstance();
    Map<String, List<String>> failedItems = {};
    
    for (var item in _items) {
      List<String> failedSubitems = [];
      
      if (item.subitems != null) {
        for (var subitem in item.subitems!) {
          // Try string value first
          final stringValue = prefs.getString('subitem_${item.title}_$subitem');
          bool isFailed = false;
          
          if (stringValue != null) {
            isFailed = stringValue == 'false';
          } else {
            try {
              // For backward compatibility, we don't have a direct "failed" state
              // in the old boolean model, so we can't determine failures
              continue;
            } catch (e) {
              continue;
            }
          }
          
          if (isFailed) {
            failedSubitems.add(subitem);
          }
        }
      }
      
      if (failedSubitems.isNotEmpty) {
        failedItems[item.title] = failedSubitems;
      }
    }
    
    return failedItems;
  }

  void _showFailedItemsReport() async {
    final failedItems = await _collectFailedItems();
    
    if (failedItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No failed items to report'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    
    showDialog(
      context: context,
      builder: (context) => FailedItemsReportDialog(
        failedItems: failedItems,
        onSendReport: _sendReportToManager,
      ),
    );
  }

  Future<void> _sendReportToManager(Map<String, List<String>> failedItems, String additionalNotes) async {
    // Here you would implement the actual reporting logic
    // This could be sending an email, creating a notification, or saving to a database
    
    // For now, we'll just show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Report sent to manager successfully'),
        backgroundColor: Colors.green,
      ),
    );
    
    // You could also save that the report was sent
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('report_sent_${DateTime.now().toIso8601String().split('T')[0]}', true);
  }

  @override
  Widget build(BuildContext context) {
    // Use orientation to adjust layout
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    
    // Use the stored progress value
    final overallProgress = _overallProgress;
    
    return WillPopScope(
      onWillPop: () async => false, // Prevent back navigation
      child: Scaffold(
        body: _isLoading 
          ? Center(child: CircularProgressIndicator())
          : Stack(
            children: [
              TiledBackground(),
              Container(
                height: isLandscape
                  ? MediaQuery.of(context).size.height * 0.25
                  : MediaQuery.of(context).size.height * 0.14,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF1E88E5),
                      Color(0xFF0D47A1),
                    ],
                  ),
                ),
              ),
              SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.checklist_rounded,
                                color: Colors.white,
                                size: isLandscape ? 30 : 36,
                              ),
                              SizedBox(width: MediaQuery.of(context).size.width * 0.01),
                              Text(
                                'Daily Checklist',
                                style: GoogleFonts.poppins(
                                  fontSize: isLandscape ? 24 : 32,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Overall Progress',
                                      style: GoogleFonts.poppins(
                                        fontSize: isLandscape ? 14 : 16,
                                        color: Colors.white70,
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Stack(
                                      children: [
                                        Container(
                                          height: 8,
                                          decoration: BoxDecoration(
                                            color: Colors.white24,
                                            borderRadius: BorderRadius.circular(4),
                                          ),
                                        ),
                                        AnimatedFractionallySizedBox(
                                          duration: Duration(milliseconds: 300),
                                          widthFactor: overallProgress,
                                          child: Container(
                                            height: 8,
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                colors: [
                                                  Color(0xFF43A047),
                                                  Color(0xFF66BB6A),
                                                ],
                                              ),
                                              borderRadius: BorderRadius.circular(4),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Color(0xFF43A047).withOpacity(0.3),
                                                  blurRadius: 8,
                                                  offset: Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      '${(overallProgress * 100).toInt()}% Complete',
                                      style: GoogleFonts.poppins(
                                        fontSize: isLandscape ? 12 : 14,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.white70,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(width: 16),
                              Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.white10,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  DateTime.now().toString().split(' ')[0],
                                  style: GoogleFonts.poppins(
                                    fontSize: isLandscape ? 12 : 14,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white70,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black87,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30),
                            topRight: Radius.circular(30),
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30),
                            topRight: Radius.circular(30),
                          ),
                          child: ListView.builder(
                            padding: EdgeInsets.symmetric(
                              horizontal: 20, 
                              vertical: isLandscape ? 16 : 24
                            ),
                            itemCount: _items.length,
                            itemBuilder: (context, index) {
                              final item = _items[index];
                              return AnimatedBuilder(
                                animation: _animation,
                                builder: (context, child) {
                                  final delay = index * 0.2;
                                  final value = _animation.value > delay
                                      ? (_animation.value - delay) / (1 - delay)
                                      : 0.0;

                                  return Transform.translate(
                                    offset: Offset(0, 20 * (1 - value)),
                                    child: Opacity(
                                      opacity: value,
                                      child: child,
                                    ),
                                  );
                                },
                                child: Padding(
                                  padding: EdgeInsets.only(bottom: isLandscape ? 12 : 16),
                                  child: GestureDetector(
                                    onTap: () => _openChecklistDetail(item),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: item.isCompleted
                                              ? [Color(0xFF43A047).withOpacity(0.2), Color(0xFF66BB6A).withOpacity(0.2)]
                                              : [Colors.white12, Colors.white10],
                                          ),
                                          borderRadius: BorderRadius.circular(16),
                                          border: Border.all(
                                            color: item.isCompleted
                                                ? Color(0xFF43A047).withOpacity(0.5)
                                                : Colors.white24,
                                            width: 1,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black12,
                                              blurRadius: 8,
                                              offset: Offset(0, 4),
                                            ),
                                          ],
                                        ),
                                        child: Padding(
                                          padding: EdgeInsets.all(isLandscape ? 12.0 : 16.0),
                                          child: Row(
                                            children: [
                                              Container(
                                                padding: EdgeInsets.all(isLandscape ? 8 : 12),
                                                decoration: BoxDecoration(
                                                  color: item.isCompleted
                                                      ? Color(0xFF43A047).withOpacity(0.1)
                                                      : Color(0xFF1E88E5).withOpacity(0.1),
                                                  borderRadius: BorderRadius.circular(12),
                                                  border: Border.all(
                                                    color: item.isCompleted
                                                        ? Color(0xFF43A047).withOpacity(0.3)
                                                        : Color(0xFF1E88E5).withOpacity(0.3),
                                                  ),
                                                ),
                                                child: Icon(
                                                  item.icon,
                                                  color: item.isCompleted
                                                      ? Color(0xFF43A047)
                                                      : Color(0xFF1E88E5),
                                                  size: isLandscape ? 24 : 28,
                                                ),
                                              ),
                                              SizedBox(width: 16),
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      item.title,
                                                      style: GoogleFonts.poppins(
                                                        fontWeight: FontWeight.w600,
                                                        fontSize: isLandscape ? 15 : 17,
                                                        color: item.isCompleted
                                                            ? Color(0xFF43A047)
                                                            : Colors.white,
                                                      ),
                                                    ),
                                                    SizedBox(height: 4),
                                                    Text(
                                                      item.subtitle,
                                                      style: GoogleFonts.poppins(
                                                        color: Colors.white70,
                                                        fontSize: isLandscape ? 12 : 14,
                                                      ),
                                                    ),
                                                    if (item.subitems != null) ...[
                                                      SizedBox(height: 8),
                                                      Row(
                                                        children: [
                                                          Container(
                                                            padding: EdgeInsets.symmetric(
                                                              horizontal: 8,
                                                              vertical: 4,
                                                            ),
                                                            decoration: BoxDecoration(
                                                              color: Colors.black26,
                                                              borderRadius: BorderRadius.circular(4),
                                                            ),
                                                            child: Text(
                                                              '${item.subitems!.length} checks required',
                                                              style: GoogleFonts.poppins(
                                                                fontSize: isLandscape ? 10 : 12,
                                                                color: Colors.white70,
                                                              ),
                                                            ),
                                                          ),
                                                          if (_isItemSigned(item.title)) ...[
                                                            SizedBox(width: 8),
                                                            Container(
                                                              padding: EdgeInsets.symmetric(
                                                                horizontal: 8,
                                                                vertical: 4,
                                                              ),
                                                              decoration: BoxDecoration(
                                                                color: Color(0xFF43A047).withOpacity(0.2),
                                                                borderRadius: BorderRadius.circular(4),
                                                                border: Border.all(
                                                                  color: Color(0xFF43A047).withOpacity(0.3),
                                                                ),
                                                              ),
                                                              child: Row(
                                                                mainAxisSize: MainAxisSize.min,
                                                                children: [
                                                                  Icon(
                                                                    Icons.draw,
                                                                    size: isLandscape ? 10 : 12,
                                                                    color: Color(0xFF43A047),
                                                                  ),
                                                                  SizedBox(width: 4),
                                                                  Text(
                                                                    'Signed',
                                                                    style: GoogleFonts.poppins(
                                                                      fontSize: isLandscape ? 10 : 12,
                                                                      color: Color(0xFF43A047),
                                                                      fontWeight: FontWeight.w500,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ],
                                                        ],
                                                      ),
                                                    ],
                                                  ],
                                                ),
                                              ),
                                              Container(
                                                width: isLandscape ? 28 : 32,
                                                height: isLandscape ? 28 : 32,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: item.isCompleted
                                                      ? Color(0xFF43A047)
                                                      : Colors.white24,
                                                ),
                                                child: Icon(
                                                  item.isCompleted ? Icons.check : Icons.arrow_forward,
                                                  color: Colors.white,
                                                  size: isLandscape ? 16 : 18,
                                                ),
                                              ),
                                            ],
                                          ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 20,
                left: 20,
                right: 20,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _allTasksCompleted
                      ? ElevatedButton(
                          onPressed: _proceedToHome,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFF43A047),
                            foregroundColor: Colors.white,
                            padding: EdgeInsets.symmetric(vertical: isLandscape ? 12 : 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 4,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.check_circle_outline, size: isLandscape ? 18 : 20),
                              SizedBox(width: 8),
                              Text(
                                'Proceed to Home',
                                style: GoogleFonts.poppins(
                                  fontSize: isLandscape ? 14 : 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        )
                      : Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                onPressed: _showFailedItemsReport,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.orange,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(vertical: isLandscape ? 12 : 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: 4,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.report_problem_outlined, size: isLandscape ? 16 : 18),
                                    SizedBox(width: 8),
                                    Text(
                                      'Report Issues',
                                      style: GoogleFonts.poppins(
                                        fontSize: isLandscape ? 12 : 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: TextButton(
                                onPressed: _skipChecklist,
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: isLandscape ? 12 : 16),
                                  backgroundColor: Colors.black26,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: BorderSide(color: Colors.grey.withOpacity(0.3)),
                                  ),
                                ),
                                child: Text(
                                  'Skip for now',
                                  style: GoogleFonts.poppins(
                                    fontSize: isLandscape ? 12 : 14,
                                    color: Colors.grey[400],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                  ],
                ),
              ),
            ],
          ),
      ),
    );
  }
}

class ChecklistDetailDialog extends StatefulWidget {
  final ChecklistItem item;
  final Map<String, bool> subitemStatus;

  const ChecklistDetailDialog({
    required this.item,
    required this.subitemStatus,
  });

  @override
  State<ChecklistDetailDialog> createState() => _ChecklistDetailDialogState();
}

class _ChecklistDetailDialogState extends State<ChecklistDetailDialog> {
  late Map<String, bool> _subitemStatus;
  bool _allCompleted = false;
  bool _isSigned = false;
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();
    _subitemStatus = Map.from(widget.subitemStatus);
    _calculateProgress();
    _checkAllCompleted();
    
    // Check if there's an existing signature
    final signaturePoints = context.read<SignatureBloc>().state.getSignaturePoints(widget.item.title);
    _isSigned = signaturePoints.isNotEmpty;
  }

  void _calculateProgress() {
    if (widget.item.subitems == null || widget.item.subitems!.isEmpty) {
      _progress = 0.0;
      return;
    }
    
    // Count items that are explicitly marked as true (passed)
    int completedItems = _subitemStatus.entries
        .where((entry) => entry.value == true)
        .length;
    
    _progress = completedItems / widget.item.subitems!.length;
  }

  void _checkAllCompleted() {
    if (widget.item.subitems == null || widget.item.subitems!.isEmpty) {
      _allCompleted = false;
      return;
    }
    
    setState(() {
      _allCompleted = _subitemStatus.values.every((completed) => completed);
    });
  }

  Future<void> _saveSubitemStatus(String subitem, String value) async {
    final prefs = await SharedPreferences.getInstance();
    // Store the string value directly
    await prefs.setString('subitem_${widget.item.title}_$subitem', value);

    setState(() {
      // Convert string to boolean for UI state
      _subitemStatus[subitem] = value == 'true';
      _calculateProgress();
      _checkAllCompleted();

      // If marked as false (fail), report to manager
      if (value == 'false') {
        _reportToManager(widget.item.title, subitem);
      }
    });
  }

  void _reportToManager(String itemTitle, String subitem) {
    // Logic to report the issue to the manager
    print('Reporting issue: $itemTitle - $subitem marked as false');
    // You can integrate email, notification, or database logging here
  }

  @override
  Widget build(BuildContext context) {
    // Check for landscape mode
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    
    // Adjust dialog size based on orientation
    final dialogWidth = isLandscape 
        ? MediaQuery.of(context).size.width * 0.6
        : MediaQuery.of(context).size.width * 0.9;
        
    final dialogHeight = isLandscape
        ? MediaQuery.of(context).size.height * 0.8
        : MediaQuery.of(context).size.height * 0.7;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: dialogWidth,
        constraints: BoxConstraints(
          maxHeight: dialogHeight,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              decoration: BoxDecoration(
                color: Color(0xFF1E88E5),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isLandscape ? 8 : 12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      widget.item.icon,
                      color: Colors.white,
                      size: isLandscape ? 20 : 24,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.item.title,
                          style: GoogleFonts.poppins(
                            fontSize: isLandscape ? 18 : 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          widget.item.subtitle,
                          style: GoogleFonts.poppins(
                            fontSize: isLandscape ? 14 : 16,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Progress Bar
            Padding(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Progress',
                    style: GoogleFonts.poppins(
                      fontSize: isLandscape ? 14 : 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: 8),
                  Stack(
                    children: [
                      Container(
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.black12,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      AnimatedFractionallySizedBox(
                        duration: Duration(milliseconds: 300),
                        widthFactor: _progress,
                        child: Container(
                          height: 8,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Color(0xFF43A047),
                                Color(0xFF66BB6A),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(4),
                            boxShadow: [
                              BoxShadow(
                                color: Color(0xFF43A047).withOpacity(0.3),
                                blurRadius: 8,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  Text(
                    '${(_progress * 100).toInt()}% Complete',
                    style: GoogleFonts.poppins(
                      fontSize: isLandscape ? 12 : 14,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
            // Subitems List
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.symmetric(
                  horizontal: isLandscape ? 16 : 20,
                  vertical: isLandscape ? 8 : 12,
                ),
                itemCount: widget.item.subitems?.length ?? 0,
                itemBuilder: (context, index) {
                  final subitem = widget.item.subitems![index];
                  final status = _subitemStatus[subitem] ?? 'not_checked'; // Default status

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            subitem,
                            style: GoogleFonts.poppins(
                              fontSize: isLandscape ? 14 : 16,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        DropdownButton<String>(
                          value: _subitemStatus[subitem] == true ? 'true' : 
                                 _subitemStatus[subitem] == false ? 'false' : 'not_checked',
                          items: const [
                            DropdownMenuItem(
                              value: 'not_checked',
                              child: Text('Not Checked'),
                            ),
                            DropdownMenuItem(
                              value: 'true',
                              child: Text('Pass ✓'),
                            ),
                            DropdownMenuItem(
                              value: 'false',
                              child: Text('Fail ✗'),
                            ),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              _saveSubitemStatus(subitem, value);
                            }
                          },
                          dropdownColor: Colors.white,
                          style: TextStyle(
                            color: _subitemStatus[subitem] == true ? Colors.green : 
                                  _subitemStatus[subitem] == false ? Colors.red : Colors.black54,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            // Signature Section
            Padding(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Signature',
                    style: GoogleFonts.poppins(
                      fontSize: isLandscape ? 14 : 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      // Open signature pad
                      showDialog(
                        context: context,
                        builder: (context) => SignaturePadDialog(
                          itemTitle: widget.item.title,
                          onSignatureSaved: (signature) {
                            setState(() {
                              _isSigned = true;
                            });
                          },
                        ),
                      );
                    },
                    child: Container(
                      padding: EdgeInsets.all(isLandscape ? 8 : 12),
                      decoration: BoxDecoration(
                        color: _isSigned ? Color(0xFF43A047) : Colors.white24,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.draw,
                        color: _isSigned ? Colors.white : Colors.black54,
                        size: isLandscape ? 20 : 24,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Action Buttons
            Padding(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(false);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[400],
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: isLandscape ? 12 : 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 4,
                    ),
                    child: Text(
                      'Cancel',
                      style: GoogleFonts.poppins(
                        fontSize: isLandscape ? 14 : 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop(true);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF43A047),
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: isLandscape ? 12 : 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 4,
                    ),
                    child: Text(
                      'Done',
                      style: GoogleFonts.poppins(
                        fontSize: isLandscape ? 14 : 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FailedItemsReportDialog extends StatefulWidget {
  final Map<String, List<String>> failedItems;
  final Function(Map<String, List<String>>, String) onSendReport;

  const FailedItemsReportDialog({
    required this.failedItems,
    required this.onSendReport,
  });

  @override
  State<FailedItemsReportDialog> createState() => _FailedItemsReportDialogState();
}

class _FailedItemsReportDialogState extends State<FailedItemsReportDialog> {
  final TextEditingController _notesController = TextEditingController();

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(isLandscape ? 16 : 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.report_problem, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'Failed Checklist Items',
                  style: GoogleFonts.poppins(
                    fontSize: isLandscape ? 18 : 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Text(
              'The following items have failed the checklist:',
              style: GoogleFonts.poppins(
                fontSize: isLandscape ? 14 : 16,
              ),
            ),
            SizedBox(height: 8),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: widget.failedItems.entries.map((entry) {
                    return Padding(
                      padding: EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            entry.key,
                            style: GoogleFonts.poppins(
                              fontSize: isLandscape ? 16 : 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.red[700],
                            ),
                          ),
                          SizedBox(height: 4),
                          ...entry.value.map((subitem) => Padding(
                            padding: EdgeInsets.only(left: 16, bottom: 4),
                            child: Row(
                              children: [
                                Icon(Icons.close, color: Colors.red, size: 16),
                                SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    subitem,
                                    style: GoogleFonts.poppins(
                                      fontSize: isLandscape ? 14 : 16,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )).toList(),
                        ],
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Additional Notes:',
              style: GoogleFonts.poppins(
                fontSize: isLandscape ? 14 : 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            TextField(
              controller: _notesController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'Add any additional information here...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: GoogleFonts.poppins(
                      color: Colors.grey[700],
                    ),
                  ),
                ),
                SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    widget.onSendReport(widget.failedItems, _notesController.text);
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[700],
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Send Report',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
