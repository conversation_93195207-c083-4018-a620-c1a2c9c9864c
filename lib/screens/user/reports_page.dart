import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../widgets/tiled_background.dart';
import '../../widgets/app_bar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/reports/reports_bloc.dart';

class ReportsPage extends StatefulWidget {
  const ReportsPage({super.key});

  @override
  State<ReportsPage> createState() => _ReportsPageState();
}

class _ReportsPageState extends State<ReportsPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String selectedDateRange = 'Today';
  String selectedReportType = 'Sales';

  @override
  void initState() {
    super.initState();
    context.read<ReportsBloc>().add(FetchReports());
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      extendBodyBehindAppBar: true,
      key: _scaffoldKey,
      appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
      body: Stack(
        children: [
          const TiledBackground(),
          SafeArea(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: isLandscape ? screenSize.width * 0.05 : 16,
                vertical: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Filters section
                  _buildFilters(),
                  const SizedBox(height: 20),

                  // Main content - using Expanded to take remaining space
                  Expanded(
                    child: isLandscape
                        ? _buildLandscapeContent()
                        : _buildPortraitContent(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: Padding(
        padding: EdgeInsets.only(
          bottom: isLandscape ? 16 : 0,
          right: isLandscape ? 24 : 0,
        ),
        child: FloatingActionButton.extended(
          onPressed: _generateReport,
          backgroundColor: Colors.indigo,
          icon: const Icon(Icons.download, color: Colors.white),
          label: Text(
            'Export Report',
            style: GoogleFonts.poppins(color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildLandscapeContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Summary cards section - 3/5 of the width
        Expanded(
          flex: 3,
          child: _buildSummaryCards(),
        ),
        const SizedBox(width: 20),
        // Reports list section - 2/5 of the width
        Expanded(
          flex: 2,
          child: _buildReportsList(),
        ),
      ],
    );
  }

  Widget _buildPortraitContent() {
    return Column(
      children: [
        // Summary cards
        _buildSummaryCards(),
        const SizedBox(height: 20),
        // Reports list - takes remaining space
        Expanded(
          child: _buildReportsList(),
        ),
      ],
    );
  }

  Widget _buildFilters() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withOpacity(0.15),
            Colors.white.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: EdgeInsets.all(isLandscape ? 24 : 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.indigo.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.filter_list,
                        color: Colors.white.withOpacity(0.9),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Report Filters',
                      style: GoogleFonts.poppins(
                        fontSize: isLandscape ? 20 : 22,
                        color: Colors.white.withOpacity(0.9),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                if (isLandscape)
                  Row(
                    children: [
                      Expanded(child: _buildDateRangeFilter()),
                      const SizedBox(width: 16),
                      Expanded(child: _buildReportTypeFilter()),
                    ],
                  )
                else
                  Column(
                    children: [
                      _buildDateRangeFilter(),
                      const SizedBox(height: 16),
                      _buildReportTypeFilter(),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 8),
          child: Text(
            'Date Range',
            style: GoogleFonts.poppins(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.black26,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedDateRange,
              dropdownColor: Colors.grey[850],
              borderRadius: BorderRadius.circular(12),
              icon: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.arrow_drop_down,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              items: [
                _buildDropdownItem('Today', Icons.today),
                _buildDropdownItem('This Week', Icons.calendar_view_week),
                _buildDropdownItem('This Month', Icons.calendar_month),
                _buildDropdownItem('Custom', Icons.date_range),
              ],
              onChanged: (String? value) {
                setState(() => selectedDateRange = value!);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReportTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 8),
          child: Text(
            'Report Type',
            style: GoogleFonts.poppins(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.black26,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.1),
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedReportType,
              dropdownColor: Colors.grey[850],
              borderRadius: BorderRadius.circular(12),
              icon: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.arrow_drop_down,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              isExpanded: true,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              items: [
                _buildDropdownItem('Sales', Icons.attach_money),
                _buildDropdownItem('Inventory', Icons.inventory_2),
                _buildDropdownItem('Staff', Icons.people),
                _buildDropdownItem('Orders', Icons.receipt_long),
              ],
              onChanged: (String? value) {
                setState(() => selectedReportType = value!);
              },
            ),
          ),
        ),
      ],
    );
  }

  DropdownMenuItem<String> _buildDropdownItem(String text, IconData icon) {
    return DropdownMenuItem<String>(
      value: text,
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.white.withOpacity(0.7),
          ),
          const SizedBox(width: 12),
          Text(
            text,
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: isLandscape ? MediaQuery.of(context).size.height * 0.5 : MediaQuery.of(context).size.height * 0.3,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: StaggeredGrid.count(
            crossAxisCount: isLandscape ? 3 : 3,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            children: [
              StaggeredGridTile.extent(
                crossAxisCellCount: 1,
                mainAxisExtent: isLandscape ? 140 : 120,
                child: _buildSummaryCard(
                  'Total Sales',
                  '\$12,345',
                  Icons.attach_money,
                  Colors.green,
                ),
              ),
              StaggeredGridTile.extent(
                crossAxisCellCount: 1,
                mainAxisExtent: isLandscape ? 140 : 120,
                child: _buildSummaryCard(
                  'Orders',
                  '234',
                  Icons.shopping_cart,
                  Colors.blue,
                ),
              ),
              StaggeredGridTile.extent(
                crossAxisCellCount: 1,
                mainAxisExtent: isLandscape ? 140 : 120,
                child: _buildSummaryCard(
                  'Average Order',
                  '\$52.75',
                  Icons.analytics,
                  Colors.orange,
                ),
              ),
              StaggeredGridTile.extent(
                crossAxisCellCount: 1,
                mainAxisExtent: isLandscape ? 140 : 120,
                child: _buildSummaryCard(
                  'Top Items',
                  '15',
                  Icons.star,
                  Colors.purple,
                ),
              ),
              StaggeredGridTile.extent(
                crossAxisCellCount: 1,
                mainAxisExtent: isLandscape ? 140 : 120,
                child: _buildSummaryCard(
                  'Active Tables',
                  '8',
                  Icons.table_restaurant,
                  Colors.teal,
                ),
              ),
              StaggeredGridTile.extent(
                crossAxisCellCount: 1,
                mainAxisExtent: isLandscape ? 140 : 120,
                child: _buildSummaryCard(
                  'Staff Active',
                  '12',
                  Icons.people,
                  Colors.deepPurple,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white24,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: EdgeInsets.all(isLandscape ? 12 : 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: isLandscape ? 28 : 32,
                ),
                SizedBox(height: isLandscape ? 8 : 12),
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontSize: isLandscape ? 20 : 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: isLandscape ? 4 : 6),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.poppins(
                    fontSize: isLandscape ? 13 : 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildReportsList() {
    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Daily Checklist Reports',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: () {
                    context.read<ReportsBloc>().add(FetchReports());
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            Expanded(
              child: BlocBuilder<ReportsBloc, ReportsState>(
                builder: (context, state) {
                  if (state is ReportsLoading) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (state is ReportsError) {
                    return Center(
                      child: Text(
                        state.message,
                        style: GoogleFonts.poppins(color: Colors.white),
                      ),
                    );
                  }

                  if (state is ReportsLoaded) {
                    if (state.reports.isEmpty) {
                      return Center(
                        child: Text(
                          'No reports available',
                          style: GoogleFonts.poppins(color: Colors.white70),
                        ),
                      );
                    }

                    return ListView.builder(
                      itemCount: state.reports.length,
                      itemBuilder: (context, index) {
                        final report = state.reports[index];
                        final fileName = report.path.split('/').last;
                        final date = fileName.replaceAll('checklist_', '').replaceAll('.pdf', '');
                        
                        return Card(
                          color: Colors.black26,
                          child: ListTile(
                            leading: const Icon(
                              Icons.description,
                              color: Colors.white70,
                            ),
                            title: Text(
                              'Daily Checklist - $date',
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            subtitle: Text(
                              'Generated on ${report.lastModifiedSync().toString().split('.')[0]}',
                              style: GoogleFonts.poppins(
                                color: Colors.white70,
                                fontSize: 12,
                              ),
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(
                                    Icons.visibility,
                                    color: Colors.blue,
                                  ),
                                  onPressed: () => _openPdfPreview(report),
                                ),
                                IconButton(
                                  icon: const Icon(
                                    Icons.download,
                                    color: Colors.green,
                                  ),
                                  onPressed: () => _downloadReport(report),
                                ),
                                IconButton(
                                  icon: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                  ),
                                  onPressed: () {
                                    showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: Text(
                                          'Delete Report',
                                          style: GoogleFonts.poppins(),
                                        ),
                                        content: Text(
                                          'Are you sure you want to delete this report?',
                                          style: GoogleFonts.poppins(),
                                        ),
                                        actions: [
                                          TextButton(
                                            onPressed: () => Navigator.pop(context),
                                            child: Text(
                                              'Cancel',
                                              style: GoogleFonts.poppins(),
                                            ),
                                          ),
                                          TextButton(
                                            onPressed: () {
                                              context.read<ReportsBloc>().add(
                                                DeleteReport(report.path),
                                              );
                                              Navigator.pop(context);
                                            },
                                            child: Text(
                                              'Delete',
                                              style: GoogleFonts.poppins(
                                                color: Colors.red,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  }

                  return const SizedBox();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportItem() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Container(
      height: isLandscape ? 56 : 64,
      margin: const EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.symmetric(
        horizontal: isLandscape ? 12 : 16,
        vertical: isLandscape ? 8 : 12,
      ),
      decoration: BoxDecoration(
        color: Colors.black26,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.description,
            color: Colors.white70,
            size: isLandscape ? 20 : 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Sales Report - June 2023',
              style: GoogleFonts.poppins(
                fontSize: isLandscape ? 14 : 16,
                color: Colors.white,
              ),
            ),
          ),
          IconButton(
            icon: Icon(Icons.download,
              size: isLandscape ? 20 : 24,
              color: Colors.white70,
            ),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  void _openPdfPreview(File file) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.zero,
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
            color: Colors.black87,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.indigo.shade900,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        file.path.split('/').last,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.download, color: Colors.white),
                      onPressed: () => _downloadReport(file),
                      tooltip: 'Download',
                    ),
                    IconButton(
                      icon: const Icon(Icons.share, color: Colors.white),
                      onPressed: () => _shareReport(file),
                      tooltip: 'Share',
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Stack(
                      children: [
                        PDFView(
                          filePath: file.path,
                          enableSwipe: true,
                          swipeHorizontal: false,
                          autoSpacing: true,
                          pageFling: true,
                          pageSnap: true,
                          defaultPage: 0,
                          fitPolicy: FitPolicy.WIDTH,
                          preventLinkNavigation: false,
                          onRender: (_pages) {
                            setState(() {
                              // You can add a loading state here if needed
                            });
                          },
                          onError: (error) {
                            print(error.toString());
                          },
                          onPageError: (page, error) {
                            print('$page: ${error.toString()}');
                          },
                          onViewCreated: (PDFViewController pdfViewController) {
                            // You can store the controller for further use
                          },
                        ),
                        Positioned(
                          bottom: 16,
                          right: 16,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.6),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.zoom_out,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  '100%',
                                  style: GoogleFonts.poppins(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                ),
                                SizedBox(width: 8),
                                Icon(Icons.zoom_in,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _generateReport() {
    // Implement report generation logic
  }

  Future<void> _downloadReport(File file) async
  {
    // Implement download logic
    final directory = await Directory.systemTemp.createTemp();
    final newFile = await file.copy('${directory.path}/${file.path.split('/').last}');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Report downloaded to ${newFile.path}')),
    );
  }

  Future<void> _shareReport(File file) async {
    // Implement share logic
  }
}
