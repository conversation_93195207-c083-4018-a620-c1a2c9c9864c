import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../widgets/tiled_background.dart';
import '../../widgets/app_bar.dart';

class SupportPage extends StatefulWidget {
  const SupportPage({super.key});

  @override
  State<SupportPage> createState() => _SupportPageState();
}

class _SupportPageState extends State<SupportPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _subjectController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final screenSize = MediaQuery.of(context).size;

    return Scaffold(
      extendBodyBehindAppBar: true,
      key: _scaffoldKey,
      appBar: WaiterAppBar(scaffoldKey: _scaffoldKey),
      body: Stack(
        children: [
          const TiledBackground(),
          SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(isLandscape ? 24 : 16),
              child: isLandscape 
                  ? _buildLandscapeLayout(screenSize)
                  : _buildPortraitLayout(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(Size screenSize) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column - Quick Actions and System Status
        Expanded(
          flex: 3,
          child: Column(
            children: [
              _buildQuickActions(),
              const SizedBox(height: 24),
              _buildSystemStatus(),
            ],
          ),
        ),
        const SizedBox(width: 24),
        // Right column - Support Ticket Form and FAQ
        Expanded(
          flex: 4,
          child: Column(
            children: [
              _buildSupportTicketForm(),
              const SizedBox(height: 24),
              _buildFAQSection(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPortraitLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        _buildQuickActions(),
        const SizedBox(height: 20),
        _buildSystemStatus(),
        const SizedBox(height: 20),
        _buildSupportTicketForm(),
        const SizedBox(height: 20),
        _buildFAQSection(),
      ],
    );
  }

  Widget _buildHeader() {
    return Text(
      'Support Center',
      style: GoogleFonts.poppins(
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildQuickActions() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(isLandscape ? 24 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: GoogleFonts.poppins(
                fontSize: isLandscape ? 22 : 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: isLandscape ? 24 : 16),
            StaggeredGrid.count(
              crossAxisCount: isLandscape ? 1 : 3,
              mainAxisSpacing: isLandscape ? 16 : 8,
              crossAxisSpacing: isLandscape ? 16 : 8,
              children: [
                StaggeredGridTile.fit(
                  crossAxisCellCount: 1,
                  child: _buildQuickActionButton(
                    'Call Support',
                    Icons.phone,
                    () => _callSupport(),
                  ),
                ),
                StaggeredGridTile.fit(
                  crossAxisCellCount: 1,
                  child: _buildQuickActionButton(
                    'Live Chat',
                    Icons.chat,
                    () => _openLiveChat(),
                  ),
                ),
                StaggeredGridTile.fit(
                  crossAxisCellCount: 1,
                  child: _buildQuickActionButton(
                    'User Guide',
                    Icons.book,
                    () => _openUserGuide(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
      String label, IconData icon, VoidCallback onPressed) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Container(
      decoration: BoxDecoration(
        color: Colors.deepOrange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(isLandscape ? 16 : 12),
          child: isLandscape
              ? Row(
                  children: [
                    Icon(icon, size: 28, color: Colors.white),
                    const SizedBox(width: 16),
                    Text(
                      label,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                )
              : Column(
                  children: [
                    Icon(icon, size: 32, color: Colors.white),
                    const SizedBox(height: 8),
                    Text(
                      label,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildSystemStatus() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(isLandscape ? 24 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Status',
              style: GoogleFonts.poppins(
                fontSize: isLandscape ? 22 : 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: isLandscape ? 24 : 16),
            _buildStatusItem('Server Status', 'Operational', Colors.green),
            _buildStatusItem('Database', 'Operational', Colors.green),
            _buildStatusItem('Payment System', 'Operational', Colors.green),
            _buildStatusItem('Order Processing', 'Minor Issues', Colors.orange),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 16,
          )),
          Chip(
            label: Text(
              status,
              style: GoogleFonts.poppins(color: Colors.white, fontSize: 12),
            ),
            backgroundColor: color,
          ),
        ],
      ),
    );
  }

  Widget _buildSupportTicketForm() {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(isLandscape ? 24 : 16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Submit a Ticket',
                style: GoogleFonts.poppins(
                  fontSize: isLandscape ? 22 : 20,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: isLandscape ? 24 : 16),
              TextFormField(
                controller: _subjectController,
                decoration: InputDecoration(
                  labelStyle: GoogleFonts.poppins(color: Colors.white),
                  labelText: 'Subject',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a subject';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(
                  labelStyle: GoogleFonts.poppins(color: Colors.white),
                  labelText: 'Description',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                maxLines: 4,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepOrange,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: _submitTicket,
                  child: Text(
                    'Submit Ticket',
                    style: GoogleFonts.poppins(fontSize: 16, color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFAQSection() {
    return Card(
      color: Colors.white24,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Frequently Asked Questions',
              style: GoogleFonts.poppins(
                fontSize: 20,
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ExpansionTile(
              title: Text('How do I reset my password?',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                  )),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'You can reset your password by clicking on the "Forgot Password" link on the login page.',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            ExpansionTile(
              title: Text('How to create a new order?',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                  )),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Navigate to the Orders section and click on the "New Order" button.',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _callSupport() {
    // Implement call support logic
  }

  void _openLiveChat() {
    // Implement live chat logic
  }

  void _openUserGuide() {
    // Implement user guide opening logic
  }

  void _submitTicket() {
    if (_formKey.currentState!.validate()) {
      // Implement ticket submission logic
    }
  }
}
