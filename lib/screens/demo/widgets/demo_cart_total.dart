import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../blocs/demopos/pos_bloc.dart';
import '../../../blocs/demopos/pos_event.dart';
import '../../../blocs/demopos/pos_state.dart';
import '../../../models/cartItem.dart';
import '../../../services/print_service.dart';

class DemoCartTotal extends StatelessWidget {
  final String orderId;
  final String orderType;
  final String tableNumber;

  const DemoCartTotal({
    super.key,
    required this.orderId,
    required this.orderType,
    required this.tableNumber,
  });

  Widget _buildPriorityDropdown(BuildContext context, DemoPOSState state) {
    return DropdownButtonFormField<int>(
      value: state.currentPriority ?? 1,
      decoration: InputDecoration(
        labelText: 'Order Priority',
        labelStyle: GoogleFonts.dmSans(
          color: Colors.white70,
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white30),
        ),
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white),
        ),
      ),
      dropdownColor: Colors.grey[850],
      style: GoogleFonts.dmSans(color: Colors.white),
      items: [
        DropdownMenuItem(
          value: 1,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.green, size: 16),
              const SizedBox(width: 8),
              Text('Normal Priority', 
                style: GoogleFonts.dmSans(color: Colors.green)),
            ],
          ),
        ),
        DropdownMenuItem(
          value: 2,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.orange, size: 16),
              const SizedBox(width: 8),
              Text('High Priority', 
                style: GoogleFonts.dmSans(color: Colors.orange)),
            ],
          ),
        ),
        DropdownMenuItem(
          value: 3,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.red, size: 16),
              const SizedBox(width: 8),
              Text('Urgent Priority', 
                style: GoogleFonts.dmSans(color: Colors.red)),
            ],
          ),
        ),
      ],
      onChanged: (value) {
        if (value != null) {
          context.read<DemoPOSBloc>().add(UpdateOrderPriority(priority: value));
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DemoPOSBloc, DemoPOSState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Priority Dropdown
              _buildPriorityDropdown(context, state),
              const SizedBox(height: 24),
              // Existing total calculations
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Subtotal',
                    style: GoogleFonts.dmSans(color: Colors.white54),
                  ),
                  Text(
                    '\$${state.total.toStringAsFixed(2)}',
                    style: GoogleFonts.dmSans(fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Tax (10%)',
                    style: GoogleFonts.dmSans(color: Colors.white54),
                  ),
                  Text(
                    '\$${(state.total * 0.1).toStringAsFixed(2)}',
                    style: GoogleFonts.dmSans(fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                ],
              ),
              const Divider(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total',
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '\$${(state.total * 1.1).toStringAsFixed(2)}',
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: state.cartItems.isEmpty || state.isProcessing
                      ? null
                      : () {
                          _printDemoBill(context, state.cartItems, state.total);
                        },
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: const Color.fromRGBO(44, 191, 90, 1),
                    foregroundColor: const Color.fromRGBO(255, 255, 255, 1),
                  ),
                  child: state.isProcessing
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text('Place Order'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

void _printDemoBill(BuildContext context, List<CartItem> items, double total) {

  // Create bill data with demo flag
  final receiptData = ReceiptData(
    restaurantName: 'EasyDine Restaurant',
    address: '123 Main Street',
    phone: '(*************',
    orderNumber: 'DEMO-${DateTime.now().millisecondsSinceEpoch.toString().substring(7)}',
    date: DateTime.now().toString().substring(0, 16),
    tableNumber: tableNumber,
    orderType: orderType,

    items: items.map((item) => ReceiptItem(
      name: item.name,
      quantity: item.quantity,
      price: item.price,
      options: item.customization?.values.map((e) => e.toString()).toList(),
    )).toList(),
    subtotal: total,
    tax: total * 0.1,
    total: total + (total * 0.1),
    isDemo: true, // Flag to indicate this is a demo bill
  );

  // Navigate to preview screen
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => ReceiptPreviewScreen(receiptData: receiptData),
    ),
  );
  
}

// Widget _buildBillPreview(ReceiptData billData) {
//   return Column(
//     crossAxisAlignment: CrossAxisAlignment.start,
//     children: [
//       // Restaurant info
//       Center(
//         child: Text(
//           billData['restaurantName'],
//           style: GoogleFonts.poppins(
//             color: Colors.white,
//             fontWeight: FontWeight.bold,
//             fontSize: 18,
//           ),
//         ),
//       ),
//       Center(
//         child: Text(
//           billData['address'],
//           style: GoogleFonts.poppins(
//             color: Colors.grey[400],
//             fontSize: 14,
//           ),
//         ),
//       ),
//       Center(
//         child: Text(
//           'Tel: ${billData['phone']}',
//           style: GoogleFonts.poppins(
//             color: Colors.grey[400],
//             fontSize: 14,
//           ),
//         ),
//       ),
//       Divider(color: Colors.grey[700], height: 24),
//
//       // Order info
//       Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Text(
//             'Order #:',
//             style: GoogleFonts.poppins(color: Colors.grey[400]),
//           ),
//           Text(
//             billData['orderNumber'],
//             style: GoogleFonts.poppins(color: Colors.white),
//           ),
//         ],
//       ),
//       SizedBox(height: 8),
//       Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Text(
//             'Date:',
//             style: GoogleFonts.poppins(color: Colors.grey[400]),
//           ),
//           Text(
//             billData['date'],
//             style: GoogleFonts.poppins(color: Colors.white),
//           ),
//         ],
//       ),
//       SizedBox(height: 8),
//       Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Text(
//             'Table:',
//             style: GoogleFonts.poppins(color: Colors.grey[400]),
//           ),
//           Text(
//             billData['tableNumber'],
//             style: GoogleFonts.poppins(color: Colors.white),
//           ),
//         ],
//       ),
//       SizedBox(height: 8),
//       Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Text(
//             'Type:',
//             style: GoogleFonts.poppins(color: Colors.grey[400]),
//           ),
//           Text(
//             billData['orderType'],
//             style: GoogleFonts.poppins(color: Colors.white),
//           ),
//         ],
//       ),
//       Divider(color: Colors.grey[700], height: 24),
//
//       // Items header
//       Row(
//         children: [
//           Expanded(
//             flex: 5,
//             child: Text(
//               'Item',
//               style: GoogleFonts.poppins(
//                 color: Colors.grey[400],
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//           ),
//           Expanded(
//             flex: 2,
//             child: Text(
//               'Qty',
//               style: GoogleFonts.poppins(
//                 color: Colors.grey[400],
//                 fontWeight: FontWeight.bold,
//               ),
//               textAlign: TextAlign.center,
//             ),
//           ),
//           Expanded(
//             flex: 3,
//             child: Text(
//               'Price',
//               style: GoogleFonts.poppins(
//                 color: Colors.grey[400],
//                 fontWeight: FontWeight.bold,
//               ),
//               textAlign: TextAlign.right,
//             ),
//           ),
//         ],
//       ),
//       SizedBox(height: 8),
//
//       // Items
//       ...List.generate(
//         billData['items'].length,
//             (index) {
//           final item = billData['items'][index];
//           return Padding(
//             padding: const EdgeInsets.only(bottom: 8.0),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Row(
//                   children: [
//                     Expanded(
//                       flex: 5,
//                       child: Text(
//                         item['name'],
//                         style: GoogleFonts.poppins(color: Colors.white),
//                       ),
//                     ),
//                     Expanded(
//                       flex: 2,
//                       child: Text(
//                         '${item['quantity']}',
//                         style: GoogleFonts.poppins(color: Colors.white),
//                         textAlign: TextAlign.center,
//                       ),
//                     ),
//                     Expanded(
//                       flex: 3,
//                       child: Text(
//                         '\$${(item['quantity'] * item['price']).toStringAsFixed(2)}',
//                         style: GoogleFonts.poppins(color: Colors.white),
//                         textAlign: TextAlign.right,
//                       ),
//                     ),
//                   ],
//                 ),
//                 if (item['options'] != null && item['options'].isNotEmpty)
//                   Padding(
//                     padding: const EdgeInsets.only(left: 16.0),
//                     child: Text(
//                       item['options'].join(', '),
//                       style: GoogleFonts.poppins(
//                         color: Colors.grey[400],
//                         fontSize: 12,
//                       ),
//                     ),
//                   ),
//               ],
//             ),
//           );
//         },
//       ),
//
//       Divider(color: Colors.grey[700], height: 24),
//
//       // Totals
//       Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Text(
//             'Subtotal:',
//             style: GoogleFonts.poppins(color: Colors.grey[400]),
//           ),
//           Text(
//             '\$${billData['subtotal'].toStringAsFixed(2)}',
//             style: GoogleFonts.poppins(color: Colors.white),
//           ),
//         ],
//       ),
//       SizedBox(height: 8),
//       Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Text(
//             'Tax (10%):',
//             style: GoogleFonts.poppins(color: Colors.grey[400]),
//           ),
//           Text(
//             '\$${billData['tax'].toStringAsFixed(2)}',
//             style: GoogleFonts.poppins(color: Colors.white),
//           ),
//         ],
//       ),
//       SizedBox(height: 8),
//       Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Text(
//             'Total:',
//             style: GoogleFonts.poppins(
//               color: Colors.white,
//               fontWeight: FontWeight.bold,
//               fontSize: 16,
//             ),
//           ),
//           Text(
//             '\$${billData['total'].toStringAsFixed(2)}',
//             style: GoogleFonts.poppins(
//               color: Colors.amber,
//               fontWeight: FontWeight.bold,
//               fontSize: 16,
//             ),
//           ),
//         ],
//       ),
//
//       SizedBox(height: 24),
//       Center(
//         child: Text(
//           'Thank you for your visit!',
//           style: GoogleFonts.poppins(
//             color: Colors.grey[400],
//             fontStyle: FontStyle.italic,
//           ),
//         ),
//       ),
//     ],
//   );
// }
}