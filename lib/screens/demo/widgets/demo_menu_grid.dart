// lib/widgets/demo_menu_grid.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../blocs/demopos/pos_bloc.dart';
import '../../../blocs/demopos/pos_state.dart';
import '../../../models/menuItem.dart';
import '../../../services/menu_service.dart';
import 'demo_menu_item_card.dart';

class DemoMenuGrid extends StatelessWidget {
  const DemoMenuGrid({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DemoPOSBloc, DemoPOSState>(
      builder: (context, state) {
        List<MenuItem> menuItems;
        
        if (state.searchQuery.isNotEmpty) {
          menuItems = state.filteredItems;
        } else if (state.selectedCategory == "All") {
          menuItems = MenuService.getAllMenuItems();
          
          // Group items by category when "All" is selected
          final Map<String, List<MenuItem>> groupedItems = {};
          for (var item in menuItems) {
            if (!groupedItems.containsKey(item.category)) {
              groupedItems[item.category] = [];
            }
            groupedItems[item.category]!.add(item);
          }
          
          // Return a ListView with category sections using MasonryGridView
          return ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: groupedItems.length,
            itemBuilder: (context, index) {
              final category = groupedItems.keys.elementAt(index);
              final categoryItems = groupedItems[category]!;
              
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16,0),
                    child: Text(
                      category,
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  MasonryGridView.count(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    crossAxisCount: MediaQuery.of(context).orientation == Orientation.portrait ? 4 : 5,
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    itemCount: categoryItems.length,
                    itemBuilder: (context, i) => DemoMenuItemCard(item: categoryItems[i]),
                  ),
                  SizedBox(height: 8),
                ],
              );
            },
          );
        } else {
          menuItems = MenuService.getMenuItems(state.selectedCategory);
        }

        if (menuItems.isEmpty && state.searchQuery.isNotEmpty) {
          return Center(
            child: Text(
              'No items found',
              style: GoogleFonts.poppins(
                color: Colors.white.withOpacity(0.5),
                fontSize: 16,
              ),
            ),
          );
        }
        
        // Regular masonry grid view for single category or search results
        return MasonryGridView.count(
          padding: const EdgeInsets.all(16),
          crossAxisCount: MediaQuery.of(context).orientation == Orientation.portrait ? 4 : 5,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          itemCount: menuItems.length,
          itemBuilder: (context, index) => DemoMenuItemCard(item: menuItems[index]),
        );
      },
    );
  }
}
