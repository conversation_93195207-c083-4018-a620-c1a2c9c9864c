import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';
import '../models/cartItem.dart';


class QuantityControls extends StatelessWidget {
  final CartItem item;

  const QuantityControls({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          _QuantityButton(
            icon: Icons.remove,
            onPressed: () {
              if (item.quantity <= 1) {
                // Remove item when quantity would go below 1
                context.read<POSBloc>().add(RemoveFromCart(item.id));
              } else {
                context.read<POSBloc>().add(
                  UpdateCartItemQuantity(
                    id: item.id,
                    quantity: item.quantity - 1,
                  ),
                );
              }
            },
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text(
              item.quantity.toString(),
              style: GoogleFonts.dmSans(
                fontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          _QuantityButton(
            icon: Icons.add,
            onPressed: () {
              context.read<POSBloc>().add(
                UpdateCartItemQuantity(
                  id: item.id,
                  quantity: item.quantity + 1,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class _QuantityButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;

  const _QuantityButton({
    required this.icon,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(icon, size: 16,color: Colors.white,),
      onPressed: onPressed,
      constraints: const BoxConstraints(
        minWidth: 32,
        minHeight: 32,
      ),
      padding: EdgeInsets.zero,
    );
  }
}
