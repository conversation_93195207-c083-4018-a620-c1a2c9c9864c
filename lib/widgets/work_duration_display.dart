import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class WorkDurationDisplay extends StatelessWidget {
  final DateTime clockInTime;
  final bool showLive;

  const WorkDurationDisplay({
    super.key,
    required this.clockInTime,
    this.showLive = true,
  });

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = (duration.inMinutes % 60);
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DateTime>(
      stream: showLive
          ? Stream.periodic(
              const Duration(minutes: 1),
              (_) => DateTime.now(),
            )
          : null,
      builder: (context, snapshot) {
        final now = DateTime.now();
        final duration = now.difference(clockInTime);

        return Column(
          children: [
            Text(
              'Work Duration',
              style: GoogleFonts.poppins(
                color: Colors.white70,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white24,
                  width: 1,
                ),
              ),
              child: Text(
                _formatDuration(duration),
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}