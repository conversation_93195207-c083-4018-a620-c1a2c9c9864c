import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class RunningOrderCard extends StatelessWidget {
  final Map<String, dynamic> order;
  static final Map<String, Color> _tableColorMap = {}; // Stores unique colors per table
  static final List<Color> _tableColors = [
    Color(0xFF4A90E2), // Modern blue
    Color(0xFF50E3C2), // Mint
    Color(0xFF9B51E0), // Purple
    Color(0xFFFF9500), // Orange
    Color(0xFF00BFA5), // Teal
    Color(0xFFFF4081), // Pink
    Color(0xFF795548), // Brown
    Color(0xFF3F51B5), // Indigo
    Color(0xFFFF5252), // Red
    Color(0xFF00BCD4), // Cyan
  ];
  static int _colorIndex = 0;

  RunningOrderCard({super.key, required this.order}) {
    // Ensure each table gets a unique color
    final String tableNumber = (order["table"] ?? "Unknown").toString();
    if (!_tableColorMap.containsKey(tableNumber)) {
      _tableColorMap[tableNumber] = _tableColors[_colorIndex];
      _colorIndex = (_colorIndex + 1) % _tableColors.length; // Cycle through colors
    }
  }

  Color getTableColor(String table) {
    return _tableColorMap[table] ?? Colors.grey; // Default color if not assigned
  }

  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "ready":
        return Color(0xFF2CBF5A);
      case "in progress":
        return Color(0xFF4A90E2);
      case "cooking":
        return Color(0xFFFF9500);
      default:
        return Color(0xFF9E9E9E);
    }
  }

  String getStatusMessage(String status) {
    switch (status) {
      case "Ready":
        return "Order is ready to be served";
      case "In Progress":
        return "Order is being prepared";
      case "Cooking":
        return "Order is being cooked";
      default:
        return "Order status unknown";
    }
  }

  @override
  Widget build(BuildContext context) {
    final String tableNumber = (order["table"] ?? "Unknown").toString();
    final String orderId = (order["id"] ?? "").toString();
    final status = (order["status"] ?? "Unknown").toString();

    int itemCount = (order['items'] as List<dynamic>? ?? [])
        .map<int>((item) => (item['quantity'] ?? 0) as int)
        .fold(0, (a, b) => a + b);

    double totalAmount = (order['items'] as List<dynamic>? ?? []).fold(0.0,
        (sum, item) {
      final price = (item['price'] ?? 0.0).toDouble();
      final quantity = (item['quantity'] ?? 1).toInt();
      return sum + (price * quantity);
    });

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Table indicator
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: _tableColorMap[tableNumber]?.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _tableColorMap[tableNumber] ?? Colors.grey,
                          width: 1.5,
                        ),
                      ),
                      child: Text(
                        "Table $tableNumber",
                        style: GoogleFonts.dmSans(
                          color: _tableColorMap[tableNumber],
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    // Status badge
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: getStatusColor(status).withOpacity(0.15),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: getStatusColor(status),
                          width: 1.5,
                        ),
                      ),
                      child: Text(
                        status,
                        style: GoogleFonts.dmSans(
                          color: getStatusColor(status),
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    Spacer(),
                    Text(
                      "#$orderId",
                      style: GoogleFonts.dmSans(
                        color: Colors.white.withOpacity(0.6),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "$itemCount items",
                          style: GoogleFonts.dmSans(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          order["type"] ?? "Unknown Type",
                          style: GoogleFonts.dmSans(
                            color: Colors.white.withOpacity(0.6),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      "\$${totalAmount.toStringAsFixed(2)}",
                      style: GoogleFonts.dmSans(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}