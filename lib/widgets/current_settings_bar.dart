import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../blocs/settings/settings_bloc.dart';
import '../utils/colors.dart';


Widget buildCurrentSettingsBar() {
  return BlocBuilder<SettingsBloc,SettingsState>(
      builder: (context,state) {
        return Card(
          elevation: 0,
          color: Colors.white24,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: primaryColor.withOpacity(0.2),
              width: 1.5,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatusIndicator(
                  icon: Icons.takeout_dining_outlined,
                  isEnabled: state.takeawayEnabled,
                  label: "Takeaway",
                ),
                _buildStatusIndicator(
                  icon: Icons.delivery_dining_outlined,
                  isEnabled: state.deliveryEnabled,
                  label: "Delivery",
                ),
                _buildStatusIndicator(
                  icon: Icons.speed_outlined,
                  isEnabled: state.rushOrderEnabled,
                  label: "Rush",
                ),
                _buildStatusIndicator(
                  icon: Icons.restaurant_menu_outlined,
                  isEnabled: state.contactlessDineInEnabled,
                  label: "Contactless Dine In",
                ),
                _buildStatusIndicator(
                  icon: Icons.pin_outlined,
                  isEnabled: state.autoGenerateOrderId,
                  label: "Auto ID",
                ),
              ],
            ),
          ),
        );
      }
  );

}
Widget _buildStatusIndicator({
  required IconData icon,
  required bool isEnabled,
  required String label,
}) {
  final Color stateColor = isEnabled ? primaryColor : Colors.red.shade300;

  return Tooltip(
    message: "$label ${isEnabled ? 'Enabled' : 'Disabled'}",
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 24,
          color: stateColor,
          semanticLabel: "$label ${isEnabled ? 'Enabled' : 'Disabled'}",
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: GoogleFonts.dmSans(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: stateColor,
          ),
        ),
      ],
    ),
  );
}