import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';

import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';
import '../blocs/pos/pos_state.dart';
import '../services/order_id_generator.dart';
import '../dialogs/order_priority_dialog.dart';
import 'extra_options_row.dart';

class CartTotal extends StatelessWidget {
  final String orderId;
  final String orderType;
  final String tableNumber;

  const CartTotal({
    super.key,
    required this.orderId,
    required this.orderType,
    required this.tableNumber,
  });

  Widget _buildPriorityDropdown(BuildContext context, POSState state) {
    return DropdownButtonFormField<int>(
      value: state.currentPriority ?? 1,
      decoration: InputDecoration(
        labelText: 'Order Priority',
        labelStyle: GoogleFonts.dmSans(
          color: Colors.white70,
        ),
        enabledBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white30),
        ),
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.white),
        ),
      ),
      dropdownColor: Colors.grey[850],
      style: GoogleFonts.dmSans(color: Colors.white),
      items: [
        DropdownMenuItem(
          value: 1,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.green, size: 16),
              const SizedBox(width: 8),
              Text('Normal Priority', 
                style: GoogleFonts.dmSans(color: Colors.green)),
            ],
          ),
        ),
        DropdownMenuItem(
          value: 2,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.orange, size: 16),
              const SizedBox(width: 8),
              Text('High Priority', 
                style: GoogleFonts.dmSans(color: Colors.orange)),
            ],
          ),
        ),
        DropdownMenuItem(
          value: 3,
          child: Row(
            children: [
              Icon(Icons.flag_outlined, color: Colors.red, size: 16),
              const SizedBox(width: 8),
              Text('Urgent Priority', 
                style: GoogleFonts.dmSans(color: Colors.red)),
            ],
          ),
        ),
      ],
      onChanged: (value) {
        if (value != null) {
          context.read<POSBloc>().add(UpdateOrderPriority(priority: value));
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<POSBloc, POSState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Priority Dropdown
              _buildPriorityDropdown(context, state),
              const SizedBox(height: 24),
              // Existing total calculations
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Subtotal',
                    style: GoogleFonts.dmSans(color: Colors.white54),
                  ),
                  Text(
                    '\$${state.total.toStringAsFixed(2)}',
                    style: GoogleFonts.dmSans(fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Tax (10%)',
                    style: GoogleFonts.dmSans(color: Colors.white54),
                  ),
                  Text(
                    '\$${(state.total * 0.1).toStringAsFixed(2)}',
                    style: GoogleFonts.dmSans(fontWeight: FontWeight.bold, color: Colors.orange),
                  ),
                ],
              ),
              const Divider(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total',
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '\$${(state.total * 1.1).toStringAsFixed(2)}',
                    style: GoogleFonts.dmSans(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: state.cartItems.isEmpty || state.isProcessing
                      ? null
                      : () {
                          context.read<POSBloc>().add(PlaceOrder(
                            orderId: orderId,
                            priority: state.currentPriority ?? 1,
                            orderType: orderType,
                            tableNumber: tableNumber,
                          ));
                        },
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: const Color.fromRGBO(44, 191, 90, 1),
                    foregroundColor: const Color.fromRGBO(255, 255, 255, 1),
                  ),
                  child: state.isProcessing
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text('Place Order'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
