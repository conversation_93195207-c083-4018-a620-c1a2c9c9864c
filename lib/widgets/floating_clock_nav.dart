// import 'package:easydine_main/blocs/session/session_state.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:go_router/go_router.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:intl/intl.dart';
// import '../router/router_constants.dart';
// import '../blocs/session/session_bloc.dart';
// import '../blocs/session/session_event.dart';
// import '../screens/auth/clock_action_page.dart';
//
// class FloatingClockNav extends StatelessWidget {
//   const FloatingClockNav({super.key});
//
//   Widget _buildWorkInfo(BuildContext context) {
//     return BlocBuilder<SessionBloc, SessionState>(
//       builder: (context, state) {
//         final sessionBloc = context.read<SessionBloc>();
//         final currentStaff = sessionBloc.state.currentStaff;
//
//         if (currentStaff == null) return const SizedBox.shrink();
//
//         final clockOutTime = state.lastClockOut[currentStaff.id];
//         final workDuration = state.lastWorkDuration[currentStaff.id];
//
//         if (clockOutTime == null || workDuration == null) return const SizedBox.shrink();
//
//         return Container(
//           padding: const EdgeInsets.all(12),
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(12),
//             color: Colors.black45,
//             border: Border.all(color: Colors.white24),
//           ),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.end,
//             children: [
//               Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Icon(Icons.access_time, color: Colors.orange, size: 16),
//                   const SizedBox(width: 8),
//                   Text(
//                     'Last Clock-out: ${DateFormat('HH:mm').format(clockOutTime)}',
//                     style: GoogleFonts.poppins(
//                       color: Colors.white70,
//                       fontSize: 12,
//                     ),
//                   ),
//                 ],
//               ),
//               const SizedBox(height: 4),
//               Row(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Icon(Icons.timer_outlined, color: Colors.green, size: 16),
//                   const SizedBox(width: 8),
//                   Text(
//                     'Hours Worked: ${_formatDuration(workDuration)}',
//                     style: GoogleFonts.poppins(
//                       color: Colors.white70,
//                       fontSize: 12,
//                     ),
//                   ),
//                 ],
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }
//
//   String _formatDuration(Duration duration) {
//     final hours = duration.inHours;
//     final minutes = duration.inMinutes % 60;
//     return '$hours:${minutes.toString().padLeft(2, '0')}';
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       crossAxisAlignment: CrossAxisAlignment.end,
//       children: [
//         _buildWorkInfo(context),
//         const SizedBox(height: 8),
//         Container(
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(20),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.2),
//                 blurRadius: 10,
//                 spreadRadius: 2,
//               ),
//             ],
//             gradient: LinearGradient(
//               colors: [
//                 Colors.indigo.withOpacity(0.9),
//                 Colors.blue.withOpacity(0.9),
//               ],
//             ),
//           ),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               _buildClockButton(
//                 context,
//                 'Clock In',
//                 Icons.login_rounded,
//                 Colors.green,
//                 ClockAction.clockIn,
//               ),
//               Container(
//                 width: 1,
//                 height: 24,
//                 color: Colors.white24,
//               ),
//               _buildClockButton(
//                 context,
//                 'Clock Out',
//                 Icons.logout_rounded,
//                 Colors.orange,
//                 ClockAction.clockOut,
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildClockButton(
//     BuildContext context,
//     String label,
//     IconData icon,
//     Color iconColor,
//     ClockAction action,
//   ) {
//     return Material(
//       color: Colors.transparent,
//       child: InkWell(
//         borderRadius: BorderRadius.circular(20),
//         onTap: () {
//           context.pushNamed(
//             RouterConstants.clockAction,
//             extra: action,
//           );
//         },
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
//           child: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Icon(
//                 icon,
//                 color: iconColor,
//                 size: 20,
//               ),
//               const SizedBox(width: 8),
//               Text(
//                 label,
//                 style: GoogleFonts.poppins(
//                   color: Colors.white,
//                   fontSize: 14,
//                   fontWeight: FontWeight.w500,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
