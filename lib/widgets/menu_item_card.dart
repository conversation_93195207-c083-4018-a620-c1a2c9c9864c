import 'package:easydine_main/blocs/customization/customization_modal.dart';
import 'package:easydine_main/widgets/quantity_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';
import '../blocs/pos/pos_state.dart';
import '../models/menuItem.dart';

class MenuItemCard extends StatefulWidget {
  final MenuItem item;

  const MenuItemCard({super.key, required this.item});

  @override
  State<MenuItemCard> createState() => _MenuItemCardState();
}

class _MenuItemCardState extends State<MenuItemCard> 
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(
      CurvedAnimation(
        parent: _scaleController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  void _showCustomizationModal(BuildContext context, MenuItem item) {
    showModalBottomSheet(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ItemCustomizationBottomSheet(item: item),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;
    final item = widget.item;
    
    return BlocBuilder<POSBloc, POSState>(
      builder: (context, state) {
        final cartItem = state.cartItems
            .where((cartItem) => cartItem.id == widget.item.id)
            .toList();
        final quantity = cartItem.isNotEmpty ? cartItem.first.quantity : 0;

        return GestureDetector(
          onTapDown: (_) => _scaleController.forward(),
          onTapUp: (_) {
            _scaleController.reverse();
            context.read<POSBloc>().add(AddToCart(
              item,
              id: item.id,
              name: item.name,
              price: item.price,
            ));
          },
          onTapCancel: () => _scaleController.reverse(),
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) => Transform.scale(
              scale: _scaleAnimation.value,
              child: child,
            ),
            child: Card(
              color: Colors.white24,
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Text(
                            widget.item.name,
                            style: GoogleFonts.dmSans(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '\$${widget.item.price.toStringAsFixed(2)}',
                          style: GoogleFonts.dmSans(
                            color: Colors.amber.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Positioned(
                  //   top: 8,
                  //   right: 8,
                  //   child: QuantitySelector(
                  //     initialQuantity: quantity,
                  //     onQuantityChanged: (newQuantity) {
                  //       if (newQuantity > 0) {
                  //         if (quantity == 0) {
                  //           context.read<POSBloc>().add(AddToCart(
                  //             widget.item,
                  //             id: widget.item.id,
                  //             name: widget.item.name,
                  //             price: widget.item.price,
                  //           ));
                  //         } else {
                  //           context.read<POSBloc>().add(
                  //             UpdateCartItemQuantity(
                  //               id: widget.item.id,
                  //               quantity: newQuantity,
                  //             ),
                  //           );
                  //         }
                  //       } else {
                  //         context.read<POSBloc>().add(
                  //           RemoveFromCart(widget.item.id),
                  //         );
                  //       }
                  //     },
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
