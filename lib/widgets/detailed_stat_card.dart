import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

Widget buildDetailedStatCard({
  required String title,
  required String value,
  required IconData icon,
  required BuildContext context,
  required Color color,
  required String subtitle,
}) {
  return Expanded(
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 24),
          SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              color: Colors.white70,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            subtitle,
            style: GoogleFonts.poppins(
              color: Colors.white38,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ),
  );
}