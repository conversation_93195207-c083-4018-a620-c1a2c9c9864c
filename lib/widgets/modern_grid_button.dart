import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ModernGridButton extends StatelessWidget {
  final String text;
  final IconData icon;
  final VoidCallback onPressed;
  final bool enabled;
  final bool isHighlighted;
  final Color? customColor;

  const ModernGridButton({
    super.key,
    required this.text,
    required this.icon,
    required this.onPressed,
    this.enabled = true,
    this.isHighlighted = false,
    this.customColor,
  });

  @override
  Widget build(BuildContext context) {
    final baseColor = customColor ?? (isHighlighted ? const Color(0xFF2CBF5A) : Colors.white);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: enabled ? onPressed : null,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            color: enabled 
                ? (isHighlighted ? baseColor.withOpacity(0.15) : Colors.white.withOpacity(0.1))
                : Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: enabled
                  ? (isHighlighted ? baseColor : Colors.white.withOpacity(0.2))
                  : Colors.grey.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: enabled
                ? [
                    BoxShadow(
                      color: (isHighlighted ? baseColor : Colors.white).withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ]
                : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: enabled
                      ? (isHighlighted ? baseColor.withOpacity(0.2) : Colors.white.withOpacity(0.1))
                      : Colors.grey.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 28,
                  color: enabled
                      ? (isHighlighted ? baseColor : Colors.white)
                      : Colors.grey,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                text,
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: enabled
                      ? (isHighlighted ? baseColor : Colors.white)
                      : Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}