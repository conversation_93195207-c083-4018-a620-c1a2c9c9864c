import 'dart:async';
import 'package:flutter/material.dart';

class QuantitySelector extends StatefulWidget {
  final Function(int) onQuantityChanged;
  final int initialQuantity;
  final int minQuantity;
  final int maxQuantity;

  const QuantitySelector({
    Key? key,
    required this.onQuantityChanged,
    this.initialQuantity = 0,
    this.minQuantity = 0,
    this.maxQuantity = 99,
  }) : super(key: key);

  @override
  State<QuantitySelector> createState() => _QuantitySelectorState();
}

class _QuantitySelectorState extends State<QuantitySelector>
    with SingleTickerProviderStateMixin {
  late int _quantity;
  late AnimationController _animationController;
  late Animation<double> _animation;
  Timer? _autoHideTimer;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _quantity = widget.initialQuantity;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    if (_quantity > 0) {
      _showExpandedView();
    }
  }

  @override
  void didUpdateWidget(QuantitySelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialQuantity != _quantity) {
      setState(() {
        _quantity = widget.initialQuantity;
      });
      if (_quantity > 0) {
        _showExpandedView();
      }
    }
  }

  @override
  void dispose() {
    _autoHideTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _showExpandedView() {
    setState(() {
      _isExpanded = true;
    });
    _animationController.forward();
    
    // Reset and start the auto-hide timer
    _autoHideTimer?.cancel();
    _autoHideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _isExpanded = false;
        });
        _animationController.reverse();
      }
    });
  }

  void _incrementQuantity() {
    if (_quantity < widget.maxQuantity) {
      setState(() {
        _quantity++;
      });
      widget.onQuantityChanged(_quantity);
      _showExpandedView();
    }
  }

  void _decrementQuantity() {
    if (_quantity > widget.minQuantity) {
      setState(() {
        _quantity--;
      });
      widget.onQuantityChanged(_quantity);
      _showExpandedView();
    }
  }

  Widget _buildExpandedControls() {
    return Container(
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white24,
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildButton(
            icon: Icons.remove,
            onPressed: _decrementQuantity,
          ),
          Container(
            constraints: const BoxConstraints(minWidth: 36),
            alignment: Alignment.center,
            child: Text(
              '$_quantity',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          _buildButton(
            icon: Icons.add,
            onPressed: _incrementQuantity,
          ),
        ],
      ),
    );
  }

  Widget _buildCompactDisplay() {
    return GestureDetector(
      onTap: _quantity > 0 ? _showExpandedView : _incrementQuantity,
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: Colors.white24,
          borderRadius: BorderRadius.circular(18),
          border: Border.all(color: Colors.grey.shade300),
        ),
        alignment: Alignment.center,
        child: _quantity == 0
            ? Icon(
                Icons.add,
                size: 18,
                color: Colors.white,
              )
            : Text(
                '$_quantity',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
      ),
    );
  }

  Widget _buildButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(18),
        child: Container(
          width: 36,
          height: 36,
          alignment: Alignment.center,
          child: Icon(
            icon,
            size: 18,
            color: Colors.black87,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return ScaleTransition(
          scale: animation,
          child: child,
        );
      },
      child: _isExpanded ? _buildExpandedControls() : _buildCompactDisplay(),
    );
  }
}
