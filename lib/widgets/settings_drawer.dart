import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../blocs/settings/settings_bloc.dart';
import '../utils/colors.dart';

class BuildSettingsDrawer extends StatefulWidget {
  const BuildSettingsDrawer({super.key});

  @override
  State<BuildSettingsDrawer> createState() => _BuildSettingsDrawerState();
}

class _BuildSettingsDrawerState extends State<BuildSettingsDrawer> {
  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.3),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(30),
              bottomRight: Radius.circular(30),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: Offset(0, 5),
              ),
            ],
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
            ),
          ),
    child: Drawer(
      width: MediaQuery.of(context).orientation == Orientation.portrait
          ? MediaQuery.of(context).size.width * 0.6
          : MediaQuery.of(context).size.width * 0.5,
      backgroundColor: Colors.transparent,
      child: SafeArea(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(Icons.settings_outlined, size: 24, color: Colors.white),
                  const SizedBox(width: 16),
                  Text(
                    "Settings",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: BlocBuilder<SettingsBloc, SettingsState>(
                builder: (context, state) {
                  return ListView(
                    padding: const EdgeInsets.all(16),
                    children: [
                      _buildDrawerToggleSection(
                        title: "Service Options",
                        toggles: [
                          buildDrawerToggle(
                            icon: Icons.takeout_dining_outlined,
                            label: "Takeaway Service",
                            isEnabled: state.takeawayEnabled,
                            onToggle: (value) {
                              context
                                  .read<SettingsBloc>()
                                  .add(ToggleTakeaway(isEnabled: value));
                            },
                          ),
                          buildDrawerToggle(
                            icon: Icons.delivery_dining_outlined,
                            label: "Delivery Service",
                            isEnabled: state.deliveryEnabled,
                            onToggle: (value) {
                              context
                                  .read<SettingsBloc>()
                                  .add(ToggleDelivery(isEnabled: value));
                            },
                          ),
                          buildDrawerToggle(
                            icon: Icons.local_restaurant,
                            label: "Contactless Dining",
                            isEnabled: state.contactlessDineInEnabled,
                            onToggle: (value) {
                              context
                                  .read<SettingsBloc>()
                                  .add(ToggleContactlessDineIn(isEnabled: value));
                            },
                          ),
                        ],
                      ),
                      const Divider(),
                      _buildDrawerToggleSection(
                        title: "Order Processing",
                        toggles: [
                          buildDrawerToggle(
                            icon: Icons.speed_outlined,
                            label: "Rush Order Handling",
                            isEnabled: state.rushOrderEnabled,
                            onToggle: (value) {
                              context
                                  .read<SettingsBloc>()
                                  .add(ToggleRushOrder(isEnabled: value));
                            },
                          ),
                          buildDrawerToggle(
                            icon: Icons.pin_outlined,
                            label: "Auto-Generate Order IDs",
                            isEnabled: state.autoGenerateOrderId,
                            onToggle: (value) {
                              context.read<SettingsBloc>().add(
                                  ToggleAutoGenerateOrderId(isEnabled: value));
                            },
                          ),
                          const Divider(),
                          buildSettingsListTile(
                            icon: Icons.edit_outlined,
                            title: "Order ID Prefix",
                            subtitle: "Current: ORD",
                            onTap: () {
                              // Show dialog to edit order ID prefix
                            },
                          ),
                          const Divider(),
                          buildSettingsListTile(
                            icon: Icons.table_bar_outlined,
                            title: "Table Management",
                            onTap: () {
                              // Navigate to table management
                            },
                          ),
                          buildSettingsListTile(
                            icon: Icons.people_outline,
                            title: "Staff Management",
                            onTap: () {
                              // Navigate to staff management
                            },
                          ),
                          buildSettingsListTile(
                            icon: Icons.restaurant_menu_outlined,
                            title: "Menu Management",
                            onTap: () {
                              // Navigate to menu management
                            },
                          ),
                          const Divider(),
                          ExpansionTile(
                              leading: Icon(Icons.bug_report_outlined,
                                  color: Colors.white),
                              title: const Text("Report Problems",
                              style: TextStyle(
                                color:   Colors.white
                              ),
                              ),
                              childrenPadding: const EdgeInsets.only(left: 16),
                              children: [
                                buildSettingsListTile(
                                  icon: Icons.system_update_outlined,
                                  title: "System Issues",
                                  compact: true,
                                  onTap: () {
                                    // Report system issues
                                  },
                                ),
                                buildSettingsListTile(
                                  icon: Icons.payment_outlined,
                                  title: "Payment Issues",
                                  compact: true,
                                  onTap: () {
                                    // Report payment issues
                                  },
                                ),
                                buildSettingsListTile(
                                  icon: Icons.inventory_outlined,
                                  title: "Inventory Issues",
                                  compact: true,
                                  onTap: () {
                                    // Report inventory issues
                                  },
                                ),
                              ]),
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    "App Version: 1.0.0",
                    style: TextStyle(
                      color: Colors.white54,
                    ),
                  ),
                  TextButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.close),
                    label: const Text("Close"),
                    style: TextButton.styleFrom(
                      foregroundColor: primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
      )
    );
  }
}

// Helper method for settings list tiles
Widget buildSettingsListTile(
    {required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    bool compact = false}) {
  return ListTile(
    leading: Icon(icon, color: Colors.white),
    title: Text(
      title,
      style: TextStyle(
        fontSize: compact ? 14 : 16,
        fontWeight: FontWeight.w500,
        color: Colors.white
      ),
    ),
    subtitle: subtitle != null ? Text(subtitle,style: TextStyle(color: Colors.white),) : null,
    trailing: const Icon(Icons.arrow_forward_ios, size: 16,color: Colors.white,),
    dense: compact,
    onTap: onTap,
  );
}

// Helper for drawer toggle sections
Widget _buildDrawerToggleSection({
  required String title,
  required List<Widget> toggles,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
        child: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
      ...toggles,
    ],
  );
}

Widget buildDrawerToggle({
  required IconData icon,
  required String label,
  required bool isEnabled,
  required Function(bool) onToggle,
}) {
  return SwitchListTile.adaptive(
    secondary: Icon(
      icon,
      color: isEnabled ? Colors.white : Colors.grey,
    ),
    title: Text(label,style: TextStyle(color: Colors.white),),
    value: isEnabled,
    activeColor: primaryColor,
    inactiveThumbColor: Colors.black54,
    inactiveTrackColor: Colors.white,
    onChanged: onToggle,
  );
}
