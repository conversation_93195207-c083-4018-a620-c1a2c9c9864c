class OrderIdGenerator {
  static const Map<String, String> orderPrefixes = {
    'dine_in': 'DIN',
    'takeaway': 'TAK',
    'delivery': 'DEL',
    'miscellaneous': 'MSC',
  };

  static String generateOrderId({
    required String orderType,
    required int priority,
    required String tableNumber,
  }) {
    final DateTime now = DateTime.now();
    final String date = '${now.day}${now.month}${now.year.toString().substring(2)}';
    final String time = '${now.hour}${now.minute}';
    final String prefix = orderPrefixes[orderType] ?? 'ORD';
    final String priorityCode = 'P$priority';
    
    return '$prefix${tableNumber}_${priorityCode}_${date}_$time';
  }
}