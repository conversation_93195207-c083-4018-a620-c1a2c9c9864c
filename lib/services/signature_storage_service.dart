import 'dart:convert';
import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';

class SignatureStorageService {
  static const String _keyPrefix = 'signature_data_';
  static const String _datePrefix = 'signature_date_';

  // Store signature with date
  static Future<bool> saveSignature(String itemId, List<Offset?> points) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      // Encode signature data
      final signatureData = _encodeSignature(points);
      
      // Store both signature and date
      await prefs.setString('$_keyPrefix$itemId', signatureData);
      await prefs.setString('$_datePrefix$itemId', today);
      
      return true;
    } catch (e) {
      print('Error saving signature: $e');
      return false;
    }
  }

  // Load signature if it's from today
  static Future<List<Offset?>> loadSignature(String itemId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];
      final savedDate = prefs.getString('$_datePrefix$itemId');
      
      // Return empty list if signature is not from today
      if (savedDate != today) {
        return [];
      }
      
      final signatureData = prefs.getString('$_keyPrefix$itemId');
      if (signatureData == null || signatureData.isEmpty) {
        return [];
      }
      
      return _decodeSignature(signatureData);
    } catch (e) {
      print('Error loading signature: $e');
      return [];
    }
  }

  // Clear signature
  static Future<bool> clearSignature(String itemId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_keyPrefix$itemId');
      await prefs.remove('$_datePrefix$itemId');
      return true;
    } catch (e) {
      print('Error clearing signature: $e');
      return false;
    }
  }

  // Clear all signatures older than today
  static Future<void> clearOldSignatures() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      final keys = prefs.getKeys().where((key) => key.startsWith(_datePrefix));
      for (final key in keys) {
        final date = prefs.getString(key);
        if (date != today) {
          final itemId = key.replaceFirst(_datePrefix, '');
          await clearSignature(itemId);
        }
      }
    } catch (e) {
      print('Error clearing old signatures: $e');
    }
  }

  // Encode signature points to string
  static String _encodeSignature(List<Offset?> points) {
    if (points.isEmpty) return '';

    final encodedPoints = points.map((point) {
      if (point == null) return 'null';
      return '${point.dx},${point.dy}';
    }).toList();

    return json.encode(encodedPoints);
  }

  // Decode signature string to points
  static List<Offset?> _decodeSignature(String encoded) {
    if (encoded.isEmpty) return [];

    try {
      final List<dynamic> points = json.decode(encoded);
      return points.map((point) {
        if (point == 'null') return null;
        final coords = point.split(',');
        return Offset(
          double.parse(coords[0]),
          double.parse(coords[1]),
        );
      }).toList();
    } catch (e) {
      print('Error decoding signature: $e');
      return [];
    }
  }

  // Verify if signature exists for today
  static Future<bool> hasValidSignature(String itemId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0];
      final savedDate = prefs.getString('$_datePrefix$itemId');
      
      if (savedDate != today) return false;
      
      final signatureData = prefs.getString('$_keyPrefix$itemId');
      return signatureData != null && signatureData.isNotEmpty;
    } catch (e) {
      print('Error checking signature: $e');
      return false;
    }
  }
}