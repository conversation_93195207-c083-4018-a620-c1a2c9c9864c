import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import '../models/checklist_report.dart';

class PDFService {
  static Future<File> generateChecklistReport(ChecklistReport report) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        build: (context) => [
          pw.Header(
            level: 0,
            child: pw.Text(
              'Daily Safety Checklist Report',
              style: pw.TextStyle(
                fontSize: 24,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
          pw.SizedBox(height: 20),
          pw.Text('Date: ${report.date}'),
          pw.Text('Completed By: ${report.completedBy}'),
          pw.Text('Time: ${report.completionTime.toString().split('.')[0]}'),
          pw.SizedBox(height: 20),
          ...report.checklistItems.entries.map((entry) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  entry.key,
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 10),
                ...entry.value.entries.map((item) {
                  return pw.Padding(
                    padding: const pw.EdgeInsets.only(left: 20, bottom: 5),
                    child: pw.Row(
                      children: [
                        pw.Container(
                          width: 20,
                          height: 20,
                          decoration: pw.BoxDecoration(
                            border: pw.Border.all(),
                          ),
                          child: item.value
                              ? pw.Center(child: pw.Text('✓'))
                              : pw.Container(),
                        ),
                        pw.SizedBox(width: 10),
                        pw.Text(item.key),
                      ],
                    ),
                  );
                }),
                pw.SizedBox(height: 20),
                if (report.signatures[entry.key] != null)
                  pw.Container(
                    height: 100,
                    decoration: pw.BoxDecoration(
                      border: pw.Border.all(),
                    ),
                    child: pw.Center(
                      child: pw.Text('Signature verified'),
                    ),
                  ),
                pw.SizedBox(height: 30),
              ],
            );
          }),
        ],
      ),
    );

    // Use a consistent naming pattern
    final fileName = 'checklist_${report.date}.pdf';
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');

    // If file exists, delete it first
    if (await file.exists()) {
      await file.delete();
    }

    // Write the new file
    await file.writeAsBytes(await pdf.save());
    return file;
  }
}
