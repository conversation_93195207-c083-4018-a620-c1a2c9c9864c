import 'package:flutter/material.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

// Enhanced receipt data model
class ReceiptData {
  final String restaurantName;
  final String address;
  final String phone;
  final String orderNumber;
  final String date;
  final String tableNumber;
  final String orderType;
  final List<ReceiptItem> items;
  final double subtotal;
  final double tax;
  final double total;
  final bool isDemo;
  final String? customerName;
  final String? paymentMethod;
  final String? serverName;

  ReceiptData({
    required this.restaurantName,
    required this.address,
    required this.phone,
    required this.orderNumber,
    required this.date,
    required this.tableNumber,
    required this.orderType,
    required this.items,
    required this.subtotal,
    required this.tax,
    required this.total,
    this.isDemo = false,
    this.customerName,
    this.paymentMethod,
    this.serverName,
  });

  // Convert Map to ReceiptData
  factory ReceiptData.fromMap(Map<String, dynamic> map) {
    return ReceiptData(
      restaurantName: map['restaurantName'],
      address: map['address'],
      phone: map['phone'],
      orderNumber: map['orderNumber'],
      date: map['date'],
      tableNumber: map['tableNumber'],
      orderType: map['orderType'],
      items: List<ReceiptItem>.from(
          map['items'].map((item) => ReceiptItem.fromMap(item))),
      subtotal: map['subtotal'],
      tax: map['tax'],
      total: map['total'],
      isDemo: map['isDemo'] ?? false,
      customerName: map['customerName'],
      paymentMethod: map['paymentMethod'],
      serverName: map['serverName'],
    );
  }
}

// Item model
class ReceiptItem {
  final String name;
  final int quantity;
  final double price;
  final List<String>? options;

  ReceiptItem({
    required this.name,
    required this.quantity,
    required this.price,
    this.options,
  });

  factory ReceiptItem.fromMap(Map<String, dynamic> map) {
    return ReceiptItem(
      name: map['name'],
      quantity: map['quantity'],
      price: map['price'],
      options: map['options'] != null
          ? List<String>.from(map['options'])
          : null,
    );
  }
}

class ReceiptPreviewScreen extends StatefulWidget {
  final ReceiptData receiptData;

  const ReceiptPreviewScreen({
    Key? key,
    required this.receiptData,
  }) : super(key: key);

  @override
  State<ReceiptPreviewScreen> createState() => _ReceiptPreviewScreenState();
}

class _ReceiptPreviewScreenState extends State<ReceiptPreviewScreen> {
  bool _isPortrait = true;
  final ReceiptPrintService _printService = ReceiptPrintService();

  @override
  Widget build(BuildContext context) {
    // Detect orientation
    _isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.receiptData.isDemo ? 'Demo Receipt Preview' : 'Receipt Preview',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: widget.receiptData.isDemo ? Colors.amber : Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.print),
            onPressed: () => _printService.printReceipt(widget.receiptData),
            tooltip: 'Print Receipt',
          ),
          IconButton(
            icon: Icon(Icons.share),
            onPressed: () => _printService.saveAndShare(widget.receiptData),
            tooltip: 'Share Receipt',
          ),
          IconButton(
            icon: Icon(Icons.save_alt),
            onPressed: () => _printService.saveReceipt(widget.receiptData),
            tooltip: 'Save Receipt',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isPortrait) {
      return _buildPortraitLayout();
    } else {
      return _buildLandscapeLayout();
    }
  }

  Widget _buildPortraitLayout() {
    return Column(
      children: [
        Expanded(
          child: _buildReceiptPreviewContent(),
        ),
        _buildBottomActions(),
      ],
    );
  }

  Widget _buildLandscapeLayout() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: _buildReceiptPreviewContent(),
        ),
        Expanded(
          flex: 2,
          child: _buildSidePanel(),
        ),
      ],
    );
  }

  Widget _buildReceiptPreviewContent() {
    return Card(
      margin: EdgeInsets.all(16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: PdfPreview(
          build: (format) => _printService.generateReceiptPdf(widget.receiptData),
          canChangeOrientation: false,
          canChangePageFormat: false,
          canDebug: false,
          pdfPreviewPageDecoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 10,
                offset: Offset(0, 5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSidePanel() {
    return Container(
      padding: EdgeInsets.all(24),
      color: Colors.grey.shade50,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Receipt Details',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 24),
          _buildDetailItem(
            icon: Icons.business,
            title: 'Restaurant',
            value: widget.receiptData.restaurantName,
          ),
          _buildDetailItem(
            icon: Icons.receipt_long,
            title: 'Order Number',
            value: widget.receiptData.orderNumber,
          ),
          _buildDetailItem(
            icon: Icons.calendar_today,
            title: 'Date',
            value: widget.receiptData.date,
          ),
          _buildDetailItem(
            icon: Icons.table_bar,
            title: 'Table',
            value: widget.receiptData.tableNumber,
          ),
          if (widget.receiptData.customerName != null)
            _buildDetailItem(
              icon: Icons.person,
              title: 'Customer',
              value: widget.receiptData.customerName!,
            ),
          if (widget.receiptData.serverName != null)
            _buildDetailItem(
              icon: Icons.room_service,
              title: 'Server',
              value: widget.receiptData.serverName!,
            ),
          if (widget.receiptData.paymentMethod != null)
            _buildDetailItem(
              icon: Icons.payment,
              title: 'Payment Method',
              value: widget.receiptData.paymentMethod!,
            ),
          Spacer(),
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey.shade700, size: 20),
          SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            icon: Icons.print,
            label: 'Print',
            onPressed: () => _printService.printReceipt(widget.receiptData),
            primary: true,
          ),
          _buildActionButton(
            icon: Icons.share,
            label: 'Share',
            onPressed: () => _printService.saveAndShare(widget.receiptData),
          ),
          _buildActionButton(
            icon: Icons.save_alt,
            label: 'Save PDF',
            onPressed: () => _printService.saveReceipt(widget.receiptData),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool primary = false,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: primary ? Colors.blue : Colors.grey.shade200,
        foregroundColor: primary ? Colors.white : Colors.black87,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

// Enhanced print service
class ReceiptPrintService {
  Future<void> printReceipt(ReceiptData receiptData) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) => generateReceiptPdf(receiptData),
      name: 'Receipt_${receiptData.orderNumber}.pdf',
    );
  }

  Future<void> saveReceipt(ReceiptData receiptData) async {
    final pdf = await generateReceiptPdf(receiptData);
    final directory = await getApplicationDocumentsDirectory();
    final file = File(
        '${directory.path}/receipt_${receiptData.orderNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(pdf);

    // Show success message
    // Note: You'll need to implement platform-specific code to open the file
  }

  Future<void> saveAndShare(ReceiptData receiptData) async {
    final pdf = await generateReceiptPdf(receiptData);
    await Printing.sharePdf(
      bytes: pdf,
      filename: 'receipt_${receiptData.orderNumber}.pdf',
    );
  }

  Future<Uint8List> generateReceiptPdf(ReceiptData receiptData) async {
    final pdf = pw.Document();
    
    // Load font
    final ttf = await rootBundle.load("assets/fonts/Poppins-Regular.ttf");
    final ttfBold = await rootBundle.load("assets/fonts/Poppins-Bold.ttf");
    final poppins = pw.Font.ttf(ttf);
    final poppinsBold = pw.Font.ttf(ttfBold);

    // Create more modern PDF content
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a5,
        build: (pw.Context context) {
          return pw.Container(
            padding: pw.EdgeInsets.all(20),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(
                color: PdfColors.grey300,
                width: 1,
              ),
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // // Demo watermark if needed
                // if (receiptData.isDemo)
                //   pw.Container(
                //     margin: pw.EdgeInsets.only(bottom: 15),
                //     padding: pw.EdgeInsets.all(8),
                //     decoration: pw.BoxDecoration(
                //       border: pw.Border.all(color: PdfColors.red),
                //       color: PdfColors.red100,
                //       borderRadius: pw.BorderRadius.circular(4),
                //     ),
                //     alignment: pw.Alignment.center,
                //     child: pw.Text(
                //       'DEMO RECEIPT - NOT VALID',
                //       style: pw.TextStyle(
                //         font: poppinsBold,
                //         color: PdfColors.red,
                //         fontSize: 12,
                //       ),
                //     ),
                //   ),

                // Header
                pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Expanded(
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Text(
                            receiptData.restaurantName,
                            style: pw.TextStyle(
                              font: poppinsBold,
                              fontSize: 18,
                            ),
                          ),
                          pw.SizedBox(height: 5),
                          pw.Text(
                            receiptData.address,
                            style: pw.TextStyle(
                              font: poppins,
                              fontSize: 10,
                              color: PdfColors.grey700,
                            ),
                          ),
                          pw.Text(
                            'Tel: ${receiptData.phone}',
                            style: pw.TextStyle(
                              font: poppins,
                              fontSize: 10,
                              color: PdfColors.grey700,
                            ),
                          ),
                        ],
                      ),
                    ),
                    pw.Container(
                      padding: pw.EdgeInsets.all(10),
                      decoration: pw.BoxDecoration(
                        color: PdfColors.blue100,
                        borderRadius: pw.BorderRadius.circular(4),
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.center,
                        children: [
                          pw.Text(
                            'ORDER',
                            style: pw.TextStyle(
                              font: poppinsBold,
                              fontSize: 12,
                              color: PdfColors.blue800,
                            ),
                          ),
                          pw.SizedBox(height: 2),
                          pw.Text(
                            '#${receiptData.orderNumber}',
                            style: pw.TextStyle(
                              font: poppinsBold,
                              fontSize: 14,
                              color: PdfColors.blue800,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                pw.SizedBox(height: 20),
                
                // Order info
                pw.Container(
                  padding: pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.grey100,
                    borderRadius: pw.BorderRadius.circular(4),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      _buildInfoColumn(
                        'Date', 
                        receiptData.date, 
                        poppins, 
                        poppinsBold
                      ),
                      _buildInfoColumn(
                        'Table', 
                        receiptData.tableNumber, 
                        poppins, 
                        poppinsBold
                      ),
                      _buildInfoColumn(
                        'Type', 
                        receiptData.orderType, 
                        poppins, 
                        poppinsBold
                      ),
                      if (receiptData.serverName != null)
                        _buildInfoColumn(
                          'Server', 
                          receiptData.serverName!, 
                          poppins, 
                          poppinsBold
                        ),
                    ],
                  ),
                ),

                pw.SizedBox(height: 20),
                
                // Items header
                pw.Container(
                  padding: pw.EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                  color: PdfColors.grey200,
                  child: pw.Row(
                    children: [
                      pw.Expanded(
                        flex: 5,
                        child: pw.Text(
                          'Item',
                          style: pw.TextStyle(
                            font: poppinsBold,
                            fontSize: 10,
                          ),
                        ),
                      ),
                      pw.Expanded(
                        flex: 2,
                        child: pw.Text(
                          'Qty',
                          style: pw.TextStyle(
                            font: poppinsBold,
                            fontSize: 10,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Expanded(
                        flex: 2,
                        child: pw.Text(
                          'Price',
                          style: pw.TextStyle(
                            font: poppinsBold,
                            fontSize: 10,
                          ),
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                      pw.Expanded(
                        flex: 3,
                        child: pw.Text(
                          'Total',
                          style: pw.TextStyle(
                            font: poppinsBold,
                            fontSize: 10,
                          ),
                          textAlign: pw.TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Items
                ...List.generate(
                  receiptData.items.length,
                  (index) {
                    final item = receiptData.items[index];
                    return pw.Container(
                      padding: pw.EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                      decoration: pw.BoxDecoration(
                        border: pw.Border(
                          bottom: pw.BorderSide(
                            color: PdfColors.grey300,
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: [
                          pw.Row(
                            children: [
                              pw.Expanded(
                                flex: 5,
                                child: pw.Text(
                                  item.name,
                                  style: pw.TextStyle(
                                    font: poppins,
                                    fontSize: 10,
                                  ),
                                ),
                              ),
                              pw.Expanded(
                                flex: 2,
                                child: pw.Text(
                                  '${item.quantity}',
                                  style: pw.TextStyle(
                                    font: poppins,
                                    fontSize: 10,
                                  ),
                                  textAlign: pw.TextAlign.center,
                                ),
                              ),
                              pw.Expanded(
                                flex: 2,
                                child: pw.Text(
                                  '\$${item.price.toStringAsFixed(2)}',
                                  style: pw.TextStyle(
                                    font: poppins,
                                    fontSize: 10,
                                  ),
                                  textAlign: pw.TextAlign.right,
                                ),
                              ),
                              pw.Expanded(
                                flex: 3,
                                child: pw.Text(
                                  '\$${(item.quantity * item.price).toStringAsFixed(2)}',
                                  style: pw.TextStyle(
                                    font: poppins,
                                    fontSize: 10,
                                  ),
                                  textAlign: pw.TextAlign.right,
                                ),
                              ),
                            ],
                          ),
                          if (item.options != null && item.options!.isNotEmpty)
                            pw.Padding(
                              padding: pw.EdgeInsets.only(left: 10, top: 2),
                              child: pw.Text(
                                item.options!.join(', '),
                                style: pw.TextStyle(
                                  font: poppins,
                                  fontSize: 8,
                                  color: PdfColors.grey700,
                                  fontStyle: pw.FontStyle.italic,
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
                
                pw.SizedBox(height: 20),
                
                // Totals
                pw.Container(
                  alignment: pw.Alignment.centerRight,
                  child: pw.Container(
                    width: 200,
                    child: pw.Column(
                      children: [
                        _buildTotalRow(
                          'Subtotal:', 
                          '\$${receiptData.subtotal.toStringAsFixed(2)}',
                          poppins,
                          false,
                        ),
                        _buildTotalRow(
                          'Tax (10%):', 
                          '\$${receiptData.tax.toStringAsFixed(2)}',
                          poppins,
                          false,
                        ),
                        pw.SizedBox(height: 4),
                        pw.Divider(color: PdfColors.grey400),
                        pw.SizedBox(height: 4),
                        _buildTotalRow(
                          'Total:', 
                          '\$${receiptData.total.toStringAsFixed(2)}',
                          poppinsBold,
                          true,
                        ),
                        if (receiptData.paymentMethod != null)
                          _buildTotalRow(
                            'Paid via:', 
                            receiptData.paymentMethod!,
                            poppins,
                            false,
                          ),
                      ],
                    ),
                  ),
                ),
                
                pw.Spacer(),
                
                // Footer
                pw.Center(
                  child: pw.Text(
                    'Thank you for your visit!',
                    style: pw.TextStyle(
                      font: poppins,
                      fontSize: 12,
                      fontStyle: pw.FontStyle.italic,
                      color: PdfColors.grey700,
                    ),
                  ),
                ),
                
                pw.SizedBox(height: 10),
                
                pw.Center(
                  child: pw.Container(
                    padding: pw.EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.grey200,
                      borderRadius: pw.BorderRadius.circular(4),
                    ),
                    child: pw.Text(
                      receiptData.restaurantName,
                      style: pw.TextStyle(
                        font: poppins,
                        fontSize: 8,
                        color: PdfColors.grey700,
                      ),
                    ),
                  ),
                ),
                
                // // Demo footer if needed
                // if (receiptData.isDemo)
                //   pw.Container(
                //     margin: pw.EdgeInsets.only(top: 15),
                //     padding: pw.EdgeInsets.all(8),
                //     decoration: pw.BoxDecoration(
                //       border: pw.Border.all(color: PdfColors.red),
                //       color: PdfColors.red100,
                //       borderRadius: pw.BorderRadius.circular(4),
                //     ),
                //     alignment: pw.Alignment.center,
                //     child: pw.Text(
                //       'THIS IS A DEMO RECEIPT - NOT VALID',
                //       style: pw.TextStyle(
                //         font: poppinsBold,
                //         color: PdfColors.red,
                //         fontSize: 8,
                //       ),
                //     ),
                //   ),
              ],
            ),
          );
        },
      ),
    );
    
    return pdf.save();
  }
  
  pw.Widget _buildInfoColumn(
    String title, 
    String value, 
    pw.Font regularFont, 
    pw.Font boldFont
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            font: regularFont,
            fontSize: 8,
            color: PdfColors.grey700,
          ),
        ),
        pw.SizedBox(height: 2),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: boldFont,
            fontSize: 10,
          ),
        ),
      ],
    );
  }
  
  pw.Widget _buildTotalRow(
    String label, 
    String value, 
    pw.Font font,
    bool isTotal,
  ) {
    return pw.Padding(
      padding: pw.EdgeInsets.symmetric(vertical: 3),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: font,
              fontSize: isTotal ? 12 : 10,
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              font: font,
              fontSize: isTotal ? 12 : 10,
            ),
          ),
        ],
      ),
    );
  }
}
