import '../models/menuItem.dart';

class MenuService {
  static final List<MenuItem> _allMenuItems = [
    // Pizza Category
    MenuItem(
      id: 'p1',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      price: 8.99,
      image: 'https://kristineskitchenblog.com/wp-content/uploads/2024/07/margherita-pizza-22-2.jpg',
      category: 'Pizza',
      description: 'A classic Neapolitan pizza featuring fresh mozzarella, vibrant tomatoes, and aromatic basil on our house-made thin crust, finished with extra virgin olive oil.',
      ingredients: ['Fresh Mozzarella', 'San Marzano Tomatoes', 'Fresh Basil', 'Olive Oil', 'House-made Dough'],
      prepTime: 15,
      rating: 4.8,
      dietaryInfo: ['Vegetarian'],
    ),
    MenuItem(
      id: 'p2',
      name: 'Pepperoni',
      price: 9.99,
      image: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQ2f38jmDq8OZhG72KpH345LC9teABsdTXrqg&s',
      category: 'Pizza',
      description: 'Our signature pizza loaded with premium pepperoni, melted mozzarella, and our secret-recipe tomato sauce, all baked to perfection in our stone oven.',
      ingredients: ['Premium Pepperoni', 'Mozzarella', 'Tomato Sauce', 'House-made Dough'],
      prepTime: 18,
      rating: 4.9,
    ),
    MenuItem(
      id: 'p3',
      name: 'BBQ Chicken',
      price: 10.99,
      image: 'https://tse4.mm.bing.net/th?id=OIP.aCxfPSdPf6ScDBvyCAW-qQHaLH&pid=Api',
      category: 'Pizza',
      description: 'Tender grilled chicken breast, sweet BBQ sauce, red onions, and cilantro, topped with a blend of mozzarella and smoked gouda cheese.',
      ingredients: ['Grilled Chicken', 'BBQ Sauce', 'Red Onions', 'Mozzarella', 'Smoked Gouda', 'Cilantro'],
      prepTime: 20,
      rating: 4.7,
    ),
    MenuItem(
      id: 'p4',
      name: 'Veggie Supreme',
      price: 11.99,
      image: 'https://tse4.mm.bing.net/th?id=OIP.DTHeNv1X_z3aeLXU5UMLxgHaJQ&pid=Api',
      category: 'Pizza',
      description: 'A colorful medley of fresh vegetables, including bell peppers, onions, mushrooms, olives, and artichoke hearts, all baked on a crispy pizza crust.',
      ingredients: ['Bell Peppers', 'Onions', 'Mushrooms', 'Olives', 'Artichoke Hearts', 'Mozzarella', 'House-made Dough'],
      prepTime: 16,
      rating: 4.6,
      dietaryInfo: ['Vegetarian'],
  
    ),
    MenuItem(
      id: 'p5',
      name: 'Meat Lovers',
      price: 12.99,
      image: 'https://tse3.mm.bing.net/th?id=OIP.I80L4HdxoKkTG_z3HV8qAwHaLG&pid=Api',
      category: 'Pizza',
      description: 'A hearty blend of pepperoni, sausage, bacon, ham, and beef, all smothered in our rich tomato sauce and melted mozzarella cheese.',
      ingredients: ['Pepperoni', 'Sausage', 'Bacon', 'Ham', 'Beef', 'Mozzarella', 'Tomato Sauce', 'House-made Dough'],
      prepTime: 22,
      rating: 4.8,

    ),
    MenuItem(
      id: 'p6',
      name: 'Seafood',
      price: 13.99,
      image: 'https://mojo.generalmills.com/api/public/content/yFWkG7p5xUmxGBaShNbjNA_gmi_hi_res_jpeg.jpeg?v=0b390e42&t=466b54bb264e48b199fc8e83ef1136b4',
      category: 'Pizza',
      description: 'A seafood lover\'s delight with shrimp, scallops, mussels, and calamari, all cooked to perfection and served on a crispy pizza crust.',
      ingredients: ['Shrimp', 'Scallops', 'Mussels', 'Calamari', 'Mozzarella', 'Tomato Sauce', 'House-made Dough'],
      prepTime: 25,
      rating: 4.7,
    ),
    MenuItem(
      id: 'p7',
      name: 'Hawaiian',
      price: 14.99,
      image: 'https://tse3.mm.bing.net/th?id=OIP.E3S_qTVt3jIGOFyiyL2-YAHaLH&pid=Api',
      category: 'Pizza',
      description: 'A tropical twist on pizza with succulent ham, fresh pineapple, and melted mozzarella, all baked to perfection on our crispy crust.',
      ingredients: ['Ham', 'Fresh Pineapple', 'Mozzarella', 'Tomato Sauce', 'House-made Dough'],
      prepTime: 17,
      rating: 4.6,
    ),

    // Burgers Category
    MenuItem(
      id: 'b1',
      name: 'Classic Cheeseburger',
      price: 5.99,
      image: 'https://leitesculinaria.com/wp-content/uploads/2020/02/classic-cheeseburger-1200.jpg',
      category: 'Burgers',
      description: 'A juicy hand-pressed Angus beef patty topped with melted cheddar, fresh lettuce, tomato, and our special sauce, served on a toasted brioche bun.',
      ingredients: ['Angus Beef', 'Cheddar Cheese', 'Lettuce', 'Tomato', 'Special Sauce', 'Brioche Bun'],
      prepTime: 12,
      rating: 4.6,
    ),
    MenuItem(
      id: 'b2',
      name: 'Veggie Burger',
      price: 6.99,
      image: 'https://www.realsimple.com/thmb/z3cQCYXTyDQS9ddsqqlTVE8fnpc=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/real-simple-mushroom-black-bean-burgers-recipe-0c365277d4294e6db2daa3353d6ff605.jpg',
      category: 'Burgers',
      description: 'House-made plant-based patty crafted from quinoa, black beans, and roasted vegetables, topped with avocado, sprouts, and vegan aioli.',
      ingredients: ['Quinoa', 'Black Beans', 'Roasted Vegetables', 'Avocado', 'Sprouts', 'Vegan Aioli'],
      prepTime: 15,
      rating: 4.5,
      dietaryInfo: ['Vegan', 'Vegetarian'],
    ),
    MenuItem(
      id: 'b3',
      name: 'Chicken Burger',
      price: 7.99,
      image: 'https://simplehomeedit.com/wp-content/uploads/2022/09/Southern-Fried-Chicken-Burger-1.webp',
      category: 'Burgers',
      description: 'Juicy grilled chicken breast, crispy bacon, melted Swiss cheese, and our tangy ranch dressing, all nestled between a soft sesame seed bun.',
      ingredients: ['Grilled Chicken', 'Crispy Bacon', 'Swiss Cheese', 'Ranch Dressing', 'Sesame Seed Bun'],
      prepTime: 18,
      rating: 4.7,
    ),

    // Drinks Category
    MenuItem(
      id: 'd1',
      name: 'Cola',
      price: 1.99,
      image: 'https://etimg.etb2bimg.com/thumb/msid-95242615,width-1200,height-900,resizemode-4/.jpg',
      category: 'Drinks',
      description: 'Ice-cold classic cola served in a chilled glass with optional lemon wedge.',
      ingredients: ['Carbonated Water', 'Cola Syrup', 'Ice'],
      prepTime: 2,
      rating: 4.5,
    ),
    MenuItem(
      id: 'd2',
      name: 'Iced Tea',
      price: 1.99,
      image: 'https://www.eatthis.com/wp-content/uploads/sites/4/2019/05/iced-tea.jpg?quality=82&strip=1',
      category: 'Drinks',
      description: 'Refreshing iced tea with a hint of lemon, served over ice with a slice of lemon.',
      ingredients: ['Tea Leaves', 'Lemon Juice', 'Ice'],
      prepTime: 3,
      rating: 4.4,
    ),

    // Desserts Category
    MenuItem(
      id: 'ds1',
      name: 'Chocolate Cake',
      price: 4.99,
      image: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSJwmSZeZ_ZFa4YuKicYispg_pIz7HCOcy5PA&s',
      category: 'Desserts',
      description: 'Rich, moist chocolate cake layered with Belgian chocolate ganache and topped with chocolate shavings. Served with vanilla bean ice cream.',
      ingredients: ['Belgian Chocolate', 'Fresh Cream', 'Vanilla Bean Ice Cream', 'Chocolate Shavings'],
      prepTime: 10,
      rating: 4.9,
      dietaryInfo: ['Vegetarian'],
    ),
    MenuItem(
      id: 'ds2',
      name: 'Ice Cream',
      price: 3.99,
      image: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQA4F2gZidrNWH_iPuypbfzcDFKGRuy-6CaTw&s',
      category: 'Desserts',
      description: 'Premium vanilla bean ice cream served with your choice of toppings and warm chocolate or caramel sauce.',
      ingredients: ['Vanilla Bean Ice Cream', 'Choice of Toppings', 'Choice of Sauce'],
      prepTime: 5,
      rating: 4.7,
      dietaryInfo: ['Vegetarian'],
    ),
  ];

  static List<MenuItem> getMenuItems(String category) {
    return _allMenuItems.where((item) => item.category == category).toList();
  }

  static List<MenuItem> getAllMenuItems() {
    return List.from(_allMenuItems);
  }

  static List<String> getAllCategories() {
    return _allMenuItems.map((item) => item.category).toSet().toList();
  }

  static MenuItem? getItemById(String id) {
    try {
      return _allMenuItems.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }
}
