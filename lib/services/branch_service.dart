import 'dart:convert';
import 'package:easydine_main/config/env_config.dart';
import 'package:easydine_main/models/branch.dart';
import 'package:easydine_main/utils/http_client.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BranchService {
  static const String _selectedBranchIdKey = 'selected_branch_id';
  static const String _selectedBranchNameKey = 'selected_branch_name';
  static const String _selectedBranchDataKey = 'selected_branch_data';

  /// Store the selected branch in SharedPreferences
  static Future<void> setSelectedBranch(Branch branch) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_selectedBranchIdKey, branch.branchId);
    await prefs.setString(_selectedBranchNameKey, branch.name);
    await prefs.setString(_selectedBranchDataKey, jsonEncode(branch.toJson()));
  }

  /// Get the selected branch ID from SharedPreferences
  static Future<String?> getSelectedBranchId() async {
    final prefs = await SharedPreferences.getInstance();
    final branchId = prefs.getString(_selectedBranchIdKey);
    print('🏢 BranchService: Retrieved branch ID: $branchId');
    return branchId;
  }

  /// Get the selected branch name from SharedPreferences
  static Future<String?> getSelectedBranchName() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_selectedBranchNameKey);
  }

  /// Get the complete selected branch data from SharedPreferences
  static Future<Branch?> getSelectedBranch() async {
    final prefs = await SharedPreferences.getInstance();
    final branchDataString = prefs.getString(_selectedBranchDataKey);

    if (branchDataString != null) {
      try {
        final branchData = jsonDecode(branchDataString);
        return Branch.fromJson(branchData);
      } catch (e) {
        print('Error parsing branch data: $e');
        return null;
      }
    }

    return null;
  }

  /// Check if a branch is selected
  static Future<bool> isBranchSelected() async {
    final branchId = await getSelectedBranchId();
    return branchId != null && branchId.isNotEmpty;
  }

  /// Clear the selected branch from SharedPreferences
  static Future<void> clearSelectedBranch() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_selectedBranchIdKey);
    await prefs.remove(_selectedBranchNameKey);
    await prefs.remove(_selectedBranchDataKey);
  }

  /// Fetch branches from backend API
  Future<List<Branch>> fetchBranches() async {
    try {
      final String baseUrl = EnvConfig.apiBaseUrl;
      final response = await HttpClientService.get('$baseUrl/branch/view_all');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['success'] == true && responseData['data'] != null) {
          final List<dynamic> branchesJson = responseData['data'];
          return branchesJson.map((json) => Branch.fromJson(json)).toList();
        } else {
          throw Exception(
              'Failed to fetch branches: ${responseData['message']}');
        }
      } else {
        throw Exception(
            'Failed to fetch branches: HTTP ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching branches: $e');
      // Return mock data as fallback
      return _getMockBranches();
    }
  }

  /// Fallback mock data when API fails
  static List<Branch> _getMockBranches() {
    return [
      Branch(
        branchId: 'BR001',
        name: 'Downtown Branch',
        isPrimary: true,
        isActive: true,
        location: const BranchLocation(lat: 40.7128, lng: -74.0060),
        country: 'United States',
        city: 'New York',
        state: 'New York',
        postalCode: '10001',
        streetName: '123 Main Street',
        houseNumber: 'H-001',
        apartment: 'Suite 100',
        locationName: 'Downtown',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Branch(
        branchId: 'BR002',
        name: 'Mall Branch',
        isPrimary: false,
        isActive: true,
        location: const BranchLocation(lat: 40.7589, lng: -73.9851),
        country: 'United States',
        city: 'New York',
        state: 'New York',
        postalCode: '10019',
        streetName: '456 Shopping Mall Avenue',
        houseNumber: 'Level 2',
        apartment: 'Store 201',
        locationName: 'Times Square Mall',
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];
  }
}
