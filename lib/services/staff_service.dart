import 'dart:convert';
import 'dart:ffi';
import 'package:easydine_main/config/env_config.dart';
import 'package:easydine_main/utils/http_client.dart';
import '../models/staff_model.dart';

class StaffService {
  final String baseUrl = EnvConfig.apiBaseUrl;

  // Store staff list for PIN verification
  List<StaffModel> staffList = [];

  Future<List<StaffModel>> fetchBranchStaffs(String? branchId) async {
    print('🔍 StaffService: Starting to fetch staff for branch: $branchId');

    if (branchId == null || branchId.isEmpty) {
      print('❌ StaffService: Branch ID is null or empty');
      throw Exception('Branch ID is required to fetch staff');
    }

    final url = '$baseUrl/staff-manage/view_all/$branchId';
    print('🌐 StaffService: Making request to: $url');

    try {
      // Add delay to make request visible in network tab
      await Future.delayed(const Duration(seconds: 2));

      final response = await HttpClientService.get(url);
      print('📡 StaffService: Response status: ${response.statusCode}');
      print('📡 StaffService: Response headers: ${response.headers}');
      print('📡 StaffService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        print('✅ StaffService: Successfully decoded response');

        // Check if response has the expected structure
        if (responseBody is Map<String, dynamic>) {
          if (responseBody['success'] == true && responseBody['data'] != null) {
            final List<dynamic> data = responseBody['data'];
            print('✅ StaffService: Found ${data.length} staff members');
            return data.map((e) => StaffModel.fromJson(e)).toList();
          } else {
            print('❌ StaffService: API returned success=false or no data');
            print('❌ StaffService: Response: $responseBody');
            throw Exception(
                'API returned error: ${responseBody['message'] ?? 'Unknown error'}');
          }
        } else if (responseBody is List) {
          // Direct list response
          print(
              '✅ StaffService: Found ${responseBody.length} staff members (direct list)');
          return responseBody.map((e) => StaffModel.fromJson(e)).toList();
        } else {
          print('❌ StaffService: Unexpected response format');
          throw Exception('Unexpected response format');
        }
      } else {
        print('❌ StaffService: HTTP error ${response.statusCode}');
        print('❌ StaffService: Error body: ${response.body}');
        throw Exception(
            'Failed to fetch staffs: HTTP ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('💥 StaffService: Exception occurred: $e');
      rethrow;
    }
  }

  Future<List<StaffModel>> fetchBranchClockedInStaffs(String? branchId) async {
    final url = '$baseUrl/staff-manage/viewAllClockedInStaff/$branchId';
    final response = await HttpClientService.get(url);
    final responseBody = jsonDecode(response.body);
    if (responseBody is Map<String, dynamic>) {
      if (responseBody['success'] == true && responseBody['data'] != null) {
        final List<dynamic> data = responseBody['data'];
        print('✅ StaffService: Found ${data.length} staff members');
        return data.map((e) => StaffModel.fromJson(e)).toList();
      } else {
        print('❌ StaffService: API returned success=false or no data');
        print('❌ StaffService: Response: $responseBody');
        throw Exception(
            'API returned error: ${responseBody['message'] ?? 'Unknown error'}');
      }
    } else if (responseBody is List) {
      // Direct list response
      print(
          '✅ StaffService: Found ${responseBody.length} staff members (direct list)');
      return responseBody.map((e) => StaffModel.fromJson(e)).toList();
    } else {
      print('❌ StaffService: Unexpected response format');
      throw Exception('Unexpected response format');
    }
  }


  // Update staff list when fetched
  Future<List<StaffModel>> fetchBranchStaffsWithCache(String? branchId) async {
    final staff = await fetchBranchStaffs(branchId);
    staffList = staff; // Cache for PIN verification
    return staff;
  }

  Future<StaffModel?> checkInStaff(
    String staffId,
    String pin,
  ) async {
    final url = '$baseUrl/attendance/check-in/$staffId';
    final now = DateTime.now();

    print('🔄 StaffService: Attempting check-in for staff $staffId');
    print('🔄 StaffService: Check-in URL: $url');

    final response = await HttpClientService.post(
      url,
      body: jsonEncode({
        'pin': int.parse(pin),
        'checkInTime': _formatTime(now),
        'date': _formatDate(now),
      }),
    );

    print('🔄 StaffService: Check-in response status: ${response.statusCode}');
    print('🔄 StaffService: Check-in response body: ${response.body}');

    if (response.statusCode == 200 || response.statusCode == 201) {
      print('✅ StaffService: Check-in successful');

      try {
        final responseData = jsonDecode(response.body);

        // Check if response has the expected structure
        if (responseData is Map<String, dynamic> &&
            responseData['success'] == true &&
            responseData['data'] != null) {
          final staffData = responseData['data'];
          print('✅ StaffService: Parsing staff data: $staffData');

          // Create StaffModel from the response data
          final staff = StaffModel.fromJson(staffData);
          print('✅ StaffService: Staff model created: ${staff.name}');

          return staff;
        } else {
          print('❌ StaffService: Invalid response structure');
          throw Exception('Invalid response structure');
        }
      } catch (e) {
        print('❌ StaffService: Error parsing response: $e');
        throw Exception('Failed to parse check-in response: $e');
      }
    } else {
      print(
          '❌ StaffService: Check-in failed with status ${response.statusCode}');
      throw Exception('Failed to check in staff: ${response.statusCode}');
    }
  }

  Future<bool> checkOutStaff(
    String staffId,
    String pin,
  ) async {
    final url = '$baseUrl/attendance/check-out/$staffId';
    final now = DateTime.now();
    final response = await HttpClientService.post(
      url,
      body: jsonEncode({
        'pin': int.parse(pin),
        'checkOutTime': _formatTime(now),
        'date': _formatDate(now),
      }),
    );
    if (response.statusCode == 200 || response.statusCode == 201) {
      return true;
    } else {
      throw Exception('Failed to Check out staff: ${response.statusCode}');
    }
  }

  Future<bool> clockInStaff(
    String staffId,
    String pin,
  ) async {
    final url = '$baseUrl/attendance/clock-in/$staffId';
    final now = DateTime.now();
    final response = await HttpClientService.post(
      url,
      body: jsonEncode({
        'pin': int.parse(pin),
        'clockInTime': _formatTime(now),
        'date': _formatDate(now),
      }),
    );
    if (response.statusCode == 200 || response.statusCode == 201) {
      return true;
    } else {
      throw Exception('Failed to clock in staff: ${response.statusCode}');
    }
  }

  Future<bool> clockOutStaff(
    String staffId,
    String pin,
  ) async {
    final url = '$baseUrl/attendance/clock-out/$staffId';
    final now = DateTime.now();
    final response = await HttpClientService.post(
      url,
      body: jsonEncode({
        'pin': int.parse(pin),
        'clockOutTime': _formatTime(now),
        'date': _formatDate(now),
      }),
    );
    if (response.statusCode == 200 || response.statusCode == 201) {
      return true;
    } else {
      throw Exception('Failed to clock out staff: ${response.statusCode}');
    }
  }

  String _formatTime(DateTime dt) {
    return dt.toLocal().toIso8601String().substring(11, 16); // HH:mm
  }

  String _formatDate(DateTime dt) {
    return dt.toIso8601String().substring(0, 10); // yyyy-MM-dd
  }
}
