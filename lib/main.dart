import 'package:easydine_main/blocs/auth/auth_bloc.dart';
import 'package:easydine_main/blocs/checklist/signature_bloc.dart';
import 'package:easydine_main/blocs/demopos/pos_bloc.dart';
import 'package:easydine_main/blocs/running_orders/running_orders_bloc.dart';
import 'package:easydine_main/blocs/running_orders/running_orders_event.dart';
import 'package:easydine_main/blocs/settings/settings_bloc.dart';
import 'package:easydine_main/blocs/table/table_bloc.dart';
import 'package:easydine_main/router/router_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'blocs/pos/pos_bloc.dart';
import 'blocs/session/session_bloc.dart';
import 'blocs/reports/reports_bloc.dart';
import 'blocs/table/table_event.dart';
import 'blocs/staff/staff_bloc.dart';
import 'blocs/attendance/attendance_bloc.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: "assets/.env");
  runApp(MultiBlocProvider(
    providers: [
      BlocProvider<AuthBloc>(
        create: (context) => AuthBloc(),
      ),
      BlocProvider<RunningOrdersBloc>(
        create: (context) => RunningOrdersBloc()..add(FetchRunningOrders()),
      ),
      BlocProvider(create: (context) => SettingsBloc()),
      BlocProvider(create: (context) => SignatureBloc()),
      BlocProvider(create: (context) => POSBloc()),
      BlocProvider(create: (context) => DemoPOSBloc()),
      BlocProvider(create: (context) => SessionBloc()),
      BlocProvider<TableBloc>(
        create: (context) => TableBloc(
          runningOrdersBloc: context.read<RunningOrdersBloc>(),
        )..add(LoadTables()),
      ),
      BlocProvider<ReportsBloc>(
        create: (context) => ReportsBloc(),
      ),
      BlocProvider<StaffBloc>(
        create: (context) => StaffBloc(),
      ),
      BlocProvider<AttendanceBloc>(
        create: (context) => AttendanceBloc(),
      ),
    ],
    child: const MyApp(),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: 'EasyDine Waiter AIO',
      routerConfig: router,
    );
  }
}
