// signature_bloc.dart
import 'package:equatable/equatable.dart';
import 'dart:ui';

// Events
abstract class SignatureEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class LoadSignature extends SignatureEvent {
  final String itemId;
  LoadSignature(this.itemId);

  @override
  List<Object?> get props => [itemId];
}

class UpdateSignature extends SignatureEvent {
  final String itemId;
  final List<Offset?> points;
  UpdateSignature(this.itemId, this.points);

  @override
  List<Object?> get props => [itemId, points];
}

class ClearSignature extends SignatureEvent {
  final String itemId;
  ClearSignature(this.itemId);

  @override
  List<Object?> get props => [itemId];
}

class InitializeDailySignatures extends SignatureEvent {
  @override
  List<Object?> get props => [];
}
