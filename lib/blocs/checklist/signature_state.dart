import 'dart:ui';

import 'package:equatable/equatable.dart';

class SignatureState extends Equatable {
  final Map<String, List<Offset?>> signatures;
  final bool isLoading;
  final String? date;  // Added to track the current date

  const SignatureState({
    this.signatures = const {},
    this.isLoading = false,
    this.date,
  });

  SignatureState copyWith({
    Map<String, List<Offset?>>? signatures,
    bool? isLoading,
    String? date,
  }) {
    return SignatureState(
      signatures: signatures ?? this.signatures,
      isLoading: isLoading ?? this.isLoading,
      date: date ?? this.date,
    );
  }

  List<Offset?> getSignaturePoints(String itemId) {
    return signatures[itemId] ?? [];
  }

  bool hasSignature(String itemId) {
    return signatures.containsKey(itemId) && signatures[itemId]!.isNotEmpty;
  }

  bool get hasAllSignatures {
    return signatures.isNotEmpty && 
           signatures.values.every((points) => points.isNotEmpty);
  }

  @override
  List<Object?> get props => [signatures, isLoading, date];
}
