import 'dart:ui';
import 'package:easydine_main/blocs/checklist/signature_event.dart';
import 'package:easydine_main/blocs/checklist/signature_state.dart';
import 'package:easydine_main/services/signature_storage_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignatureBloc extends Bloc<SignatureEvent, SignatureState> {
  SignatureBloc() : super(const SignatureState()) {


    on<LoadSignature>(_onLoadSignature);
    on<UpdateSignature>(_onUpdateSignature);
    on<ClearSignature>(_onClearSignature);
    on<InitializeDailySignatures>(_onInitializeDailySignatures);
    
    // Initialize daily signatures when bloc is created
    add(InitializeDailySignatures());
  }

  Future<void> _onInitializeDailySignatures(
    InitializeDailySignatures event,
    Emitter<SignatureState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    try {
      // Clear old signatures and initialize new daily storage
      await SignatureStorageService.clearOldSignatures();
      
      // Load today's signatures if they exist
      final todaySignatures = Map<String, List<Offset?>>(); // Initialize empty map
      
      emit(state.copyWith(
        signatures: todaySignatures,
        isLoading: false,
        date: DateTime.now().toIso8601String().split('T')[0],
      ));
    } catch (e) {
      print('Error initializing daily signatures: $e');
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> _onLoadSignature(
    LoadSignature event,
    Emitter<SignatureState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    try {
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      // Check if we need to reset for a new day
      if (state.date != today) {
        add(InitializeDailySignatures());
        return;
      }

      final points = await SignatureStorageService.loadSignature(event.itemId);
      
      Map<String, List<Offset?>> updatedSignatures = Map.from(state.signatures);
      updatedSignatures[event.itemId] = points;

      emit(state.copyWith(
        signatures: updatedSignatures,
        isLoading: false,
      ));
    } catch (e) {
      print('Error loading signature: $e');
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> _onUpdateSignature(
    UpdateSignature event,
    Emitter<SignatureState> emit,
  ) async {
    final today = DateTime.now().toIso8601String().split('T')[0];
    
    // Check if we need to reset for a new day
    if (state.date != today) {
      add(InitializeDailySignatures());
      return;
    }

    Map<String, List<Offset?>> updatedSignatures = Map.from(state.signatures);
    updatedSignatures[event.itemId] = event.points;

    emit(state.copyWith(
      signatures: updatedSignatures,
      date: today,
    ));

    // Save to persistent storage with date
    await SignatureStorageService.saveSignature(
      event.itemId,
      event.points,
    );
  }

  Future<void> _onClearSignature(
    ClearSignature event,
    Emitter<SignatureState> emit,
  ) async {
    final today = DateTime.now().toIso8601String().split('T')[0];
    
    Map<String, List<Offset?>> updatedSignatures = Map.from(state.signatures);
    updatedSignatures[event.itemId] = [];

    emit(state.copyWith(
      signatures: updatedSignatures,
      date: today,
    ));

    // Clear from persistent storage
    await SignatureStorageService.clearSignature(event.itemId);
  }
}
