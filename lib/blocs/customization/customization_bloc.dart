// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import '../../model/menuItem.dart';
// import '../../model/menu_customization.dart';
// import 'customization_event.dart';
//
//
// class CustomizationBloc extends Bloc<CustomizationEvent, MenuItemCustomization> {
//   CustomizationBloc(MenuItem item) : super(MenuItemCustomization(
//     id: item.id,
//     name: item.name,
//     allergies: _getAllergies(item.category),
//     sides: _getSides(item.category),
//     cookingStyles: _getCookingStyles(item.category),
//   )) {
//     on<ToggleAllergy>(_onToggleAllergy);
//     on<SelectSide>(_onSelectSide);
//     on<SelectCookingStyle>(_onSelectCookingStyle);
//   }
//
//   static List<Map<String, String>> _getAllergies(String category) {
//     final allergiesMap = {
//       'Pizza': [
//         {'name': 'Gluten', 'image': 'https://example.com/gluten.png'},
//         {'name': 'Dairy', 'image': 'https://example.com/dairy.png'},
//         {'name': 'Nuts', 'image': 'https://example.com/nuts.png'}
//       ],
//       'Burgers': [
//         {'name': 'Gluten', 'image': 'https://example.com/gluten.png'},
//         {'name': 'Dairy', 'image': 'https://example.com/dairy.png'},
//         {'name': 'Egg', 'image': 'https://example.com/egg.png'}
//       ],
//       'Drinks': [
//         {'name': 'Lactose', 'image': 'https://example.com/lactose.png'}
//       ],
//       'Desserts': [
//         {'name': 'Nuts', 'image': 'https://example.com/nuts.png'},
//         {'name': 'Gluten', 'image': 'https://example.com/gluten.png'},
//         {'name': 'Dairy', 'image': 'https://example.com/dairy.png'}
//       ],
//     };
//     return (allergiesMap[category] ?? []).cast<Map<String, String>>();
//   }
//
//   static List<Map<String, String>> _getSides(String category) {
//     final sidesMap = {
//       'Pizza': [
//         {'name': 'Fries', 'image': 'https://example.com/fries.png'},
//         {'name': 'Onion Rings', 'image': 'https://example.com/onion-rings.png'},
//         {'name': 'Salad', 'image': 'https://example.com/salad.png'}
//       ],
//       'Burgers': [
//         {'name': 'Fries', 'image': 'https://example.com/fries.png'},
//         {'name': 'Onion Rings', 'image': 'https://example.com/onion-rings.png'},
//         {'name': 'Salad', 'image': 'https://example.com/salad.png'}
//       ],
//       'Drinks': [
//         {'name': 'Coke', 'image': 'https://example.com/coke.png'},
//         {'name': 'Pepsi', 'image': 'https://example.com/pepsi.png'},
//         {'name': 'Sprite', 'image': 'https://example.com/sprite.png'}
//       ],
//       'Desserts': [
//         {'name': 'Ice Cream', 'image': 'https://example.com/ice-cream.png'},
//         {'name': 'Cake', 'image': 'https://example.com/cake.png'},
//         {'name': 'Brownie', 'image': 'https://example.com/brownie.png'}
//       ]
//     };
//     return (sidesMap[category] ?? []).cast<Map<String, String>>();
//   }
//
//   static List<String> _getCookingStyles(String category) {
//     final cookingMap = {
//       'Pizza': [
//         {'name': 'Wood Fired', 'image': 'https://example.com/wood-fired.png'},
//         {'name': 'Deep Dish', 'image': 'https://example.com/deep-dish.png'},
//         {'name': 'Thin Crust', 'image': 'https://example.com/thin-crust.png'}
//       ],
//       'Burgers': [
//         {'name': 'Grilled', 'image': 'https://example.com/grilled.png'},
//         {'name': 'Fried', 'image': 'https://example.com/fried.png'},
//         {'name': 'Steamed', 'image': 'https://example.com/steamed.png'}
//       ],
//       'Drinks': [
//         {'name': 'Chilled', 'image': 'https://example.com/chilled.png'},
//         {'name': 'Frozen', 'image': 'https://example.com/frozen.png'},
//         {'name': 'Shaken', 'image': 'https://example.com/shaken.png'}
//       ],
//       'Desserts': [
//         {'name': 'Baked', 'image': 'https://example.com/baked.png'},
//         {'name': 'Chilled', 'image': 'https://example.com/chilled.png'},
//         {'name': 'Frozen', 'image': 'https://example.com/frozen.png'}
//       ]
//     };
//     return (cookingMap[category] ?? []).cast<Map<String, String>>();
//   }
//
//   void _onToggleAllergy(ToggleAllergy event, Emitter<MenuItemCustomization> emit) {
//     final currentAllergies = List<Map<String, String>>.from(state.selectedAllergies);
//     final allergyToToggle = state.allergies.firstWhere((a) => a['name'] == event.allergy);
//
//     if (currentAllergies.any((a) => a['name'] == event.allergy)) {
//       currentAllergies.removeWhere((a) => a['name'] == event.allergy);
//     } else {
//       currentAllergies.add(allergyToToggle);
//     }
//
//     emit(state.copyWith(selectedAllergies: currentAllergies));
//   }
//
//   void _onSelectSide(SelectSide event, Emitter<MenuItemCustomization> emit) {
//     final selectedSide = state.sides.firstWhere((s) => s['name'] == event.side);
//     emit(state.copyWith(selectedSide: selectedSide));
//   }
//
//   void _onSelectCookingStyle(SelectCookingStyle event, Emitter<MenuItemCustomization> emit) {
//     final selectedStyle = state.cookingStyles.firstWhere((s) => s['name'] == event.cookingStyle);
//     emit(state.copyWith(selectedCookingStyle: selectedStyle));
//   }
// }