import 'package:equatable/equatable.dart';

import '../../models/cartItem.dart';
import '../../models/menuItem.dart';

class DemoPOSState extends Equatable {
  final List<CartItem> cartItems;
  final bool isProcessing;
  final String? error;
  final String? currentOrderId;
  final int? currentPriority;
  final String selectedCategory;
  final String searchQuery;
  final List<MenuItem> filteredItems;

  const DemoPOSState({
    this.cartItems = const [],
    this.isProcessing = false,
    this.error,
    this.currentOrderId,
    this.currentPriority,
    this.selectedCategory = "All",
    this.searchQuery = '',
    this.filteredItems = const [],
  });

  double get total => cartItems.fold(0, (sum, item) => sum + item.total);

  DemoPOSState copyWith({
    List<CartItem>? cartItems,
    bool? isProcessing,
    String? error,
    String? currentOrderId,
    int? currentPriority,
    String? selectedCategory,
    String? searchQuery,
    List<MenuItem>? filteredItems,
  }) {
    return DemoPOSState(
      cartItems: cartItems ?? this.cartItems,
      isProcessing: isProcessing ?? this.isProcessing,
      error: error,
      currentOrderId: currentOrderId,
      currentPriority: currentPriority,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      searchQuery: searchQuery ?? this.searchQuery,
      filteredItems: filteredItems ?? this.filteredItems,
    );
  }

  @override
  List<Object?> get props => [
        cartItems,
        isProcessing,
        error,
        currentOrderId,
        currentPriority,
        selectedCategory,
        searchQuery,
        filteredItems,
      ];
}
