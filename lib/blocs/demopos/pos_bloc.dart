import 'dart:convert';

import 'package:easydine_main/models/cartItem.dart';
import 'package:easydine_main/services/menu_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'pos_event.dart';
import 'pos_state.dart';

class DemoPOSBloc extends Bloc<DemoPOSEvent, DemoPOSState> {
  DemoPOSBloc() : super(const DemoPOSState()) {
    on<CategorySelected>(_onCategorySelected);
    on<SearchMenuItems>(_onSearchMenuItems);
    on<AddToCart>(_onAddToCart);
    on<UpdateCartItemQuantity>(_onUpdateCartItemQuantity);
    on<RemoveFromCart>(_onRemoveFromCart);
    on<ClearCart>(_onClearCart);
    on<PlaceOrder>(_onPlaceOrder);
    on<UpdateOrderPriority>(_onUpdateOrderPriority);
  }

  void _onCategorySelected(CategorySelected event, Emitter<DemoPOSState> emit) {
    emit(state.copyWith(
      selectedCategory: event.category,
      searchQuery: '',
      filteredItems: [],
    ));
  }

  void _onSearchMenuItems(SearchMenuItems event, Emitter<DemoPOSState> emit) {
    final query = event.query.toLowerCase();
    
    if (query.isEmpty) {
      emit(state.copyWith(
        searchQuery: '',
        filteredItems: [],
        selectedCategory: null,
      ));
      return;
    }

    final allItems = MenuService.getAllMenuItems();
    final filteredItems = allItems.where((item) {
      return item.name.toLowerCase().contains(query) ||
             item.description.toLowerCase().contains(query) ||
             item.category.toLowerCase().contains(query);
    }).toList();

    emit(state.copyWith(
      searchQuery: query,
      filteredItems: filteredItems,
      selectedCategory: null,
    ));
  }

  void _onAddToCart(AddToCart event, Emitter<DemoPOSState> emit) {
    final List<CartItem> updatedCart = List.from(state.cartItems);
    
    // Check if the exact same item with same customizations exists
    final existingItemIndex = state.cartItems.indexWhere((item) => 
      item.id == event.id && 
      _areCustomizationsEqual(item.customization, event.customization)
    );

    if (existingItemIndex != -1) {
      // Update existing item quantity
      final existingItem = updatedCart[existingItemIndex];
      updatedCart[existingItemIndex] = existingItem.copyWith(
        quantity: existingItem.quantity + 1,
      );
    } else {
      // Generate a unique ID for items with different customizations
      final uniqueId = event.customization != null 
        ? '${event.id}_${DateTime.now().millisecondsSinceEpoch}'
        : event.id;
        
      // Add as new item
      updatedCart.add(CartItem(
        id: uniqueId,
        name: event.name,
        price: event.price,
        quantity: 1,
        customization: event.customization,
      ));
    }

    emit(state.copyWith(cartItems: updatedCart));
  }

  void _onUpdateCartItemQuantity(UpdateCartItemQuantity event, Emitter<DemoPOSState> emit) {
    final updatedCart = state.cartItems.map((item) {
      if (item.id == event.id) {
        return item.copyWith(quantity: event.quantity);
      }
      return item;
    }).toList();

    emit(state.copyWith(cartItems: updatedCart));
  }

  void _onRemoveFromCart(RemoveFromCart event, Emitter<DemoPOSState> emit) {
    final updatedCart = state.cartItems.where((item) => item.id != event.id).toList();
    emit(state.copyWith(cartItems: updatedCart));
  }

  void _onClearCart(ClearCart event, Emitter<DemoPOSState> emit) {
    emit(state.copyWith(cartItems: []));
  }

  Future<void> _onPlaceOrder(PlaceOrder event, Emitter<DemoPOSState> emit) async {
    if (state.cartItems.isEmpty) return;

    emit(state.copyWith(isProcessing: true));

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      emit(state.copyWith(
        cartItems: [],
        isProcessing: false,
        currentOrderId: event.orderId,
        currentPriority: event.priority,
      ));
    } catch (e) {
      emit(state.copyWith(
        isProcessing: false,
        error: 'Failed to place order',
      ));
    }
  }

  void _onUpdateOrderPriority(UpdateOrderPriority event, Emitter<DemoPOSState> emit) {
    emit(state.copyWith(currentPriority: event.priority));
  }

  // Helper method to compare customizations
  bool _areCustomizationsEqual(
    Map<String, dynamic>? customization1,
    Map<String, dynamic>? customization2,
  ) {
    if (customization1 == null && customization2 == null) return true;
    if (customization1 == null || customization2 == null) return false;
    
    // Convert to string for deep comparison
    return jsonEncode(customization1) == jsonEncode(customization2);
  }
}
