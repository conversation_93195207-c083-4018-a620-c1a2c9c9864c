import 'package:easydine_main/blocs/demopos/pos_bloc.dart';
import 'package:easydine_main/blocs/demopos/pos_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import '../../screens/demo/widgets/menu_section.dart';
import '../../widgets/menu_section.dart';

class DemoPOSScreen extends StatelessWidget {
  const DemoPOSScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      resizeToAvoidBottomInset: false,
      body: BlocListener<DemoPOSBloc, DemoPOSState>(
        listener: (context, state) {
          if (state.error != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.error!)),
            );
          }
        },
        child: DemoMenuSection(),
      ),
    );
  }
}
