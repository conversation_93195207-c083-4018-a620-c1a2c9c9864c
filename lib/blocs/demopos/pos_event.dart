import 'package:easydine_main/models/menuItem.dart';
import 'package:equatable/equatable.dart';

abstract class DemoPOSEvent extends Equatable {
  const DemoPOSEvent();

  @override
  List<Object?> get props => [];
}

class CategorySelected extends DemoPOSEvent {
  final String category;
  const CategorySelected(this.category);

  @override
  List<Object> get props => [category];
}

class AddToCart extends DemoPOSEvent {
  final String id;
  final String name;
  final double price;
  final Map<String, dynamic>? customization;

  const AddToCart(MenuItem menuItem, {
    required this.id,
    required this.name,
    required this.price,
    this.customization,
  });

  @override
  List<Object?> get props => [id, name, price, customization];
}

class UpdateCartItemQuantity extends DemoPOSEvent {
  final String id;
  final int quantity;

  const UpdateCartItemQuantity({
    required this.id,
    required this.quantity,
  });

  @override
  List<Object> get props => [id, quantity];
}

class RemoveFrom<PERSON>art extends DemoPOSEvent {
  final String id;
  const RemoveFromCart(this.id);

  @override
  List<Object> get props => [id];
}

class ClearCart extends DemoPOSEvent {}

class PlaceOrder extends DemoPOSEvent {
  final String orderId;
  final int priority;
  final String orderType;
  final String tableNumber;

  const PlaceOrder({
    required this.orderId,
    required this.priority,
    required this.orderType,
    required this.tableNumber,
  });

  @override
  List<Object> get props => [orderId, priority, orderType, tableNumber];
}

class LengthOfCartItems extends DemoPOSEvent {
  final int length;

  LengthOfCartItems(this.length);

  @override
  List<Object?> get props => [length];
}

class UpdateOrderPriority extends DemoPOSEvent {
  final int priority;

  const UpdateOrderPriority({required this.priority});

  @override
  List<Object> get props => [priority];
}

class SearchMenuItems extends DemoPOSEvent {
  final String query;
  
  const SearchMenuItems(this.query);
}
