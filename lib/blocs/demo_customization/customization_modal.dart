import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/menuItem.dart';
import '../pos/pos_bloc.dart';
import '../pos/pos_event.dart';

class DemoItemCustomizationBottomSheet extends StatefulWidget {
  final MenuItem item;

  const DemoItemCustomizationBottomSheet({
    super.key,
    required this.item,
  });

  @override
  State<DemoItemCustomizationBottomSheet> createState() => _DemoItemCustomizationBottomSheetState();
}

class _DemoItemCustomizationBottomSheetState extends State<DemoItemCustomizationBottomSheet> {
  // Cooking Options
  final List<String> cookingOptions = ['Well Done', 'Medium', 'Rare', 'No Preference'];
  String? selectedCookingOption;

  // Side Options with Images
  final List<Map<String, dynamic>> sideOptions = [
    {
      'name': 'French Fries',
      'image': 'https://www.awesomecuisine.com/wp-content/uploads/2009/05/french-fries.jpg',
      'price': 2.99
    },
    {
      'name': 'Salad',
      'image': 'https://i2.wp.com/www.downshiftology.com/wp-content/uploads/2018/08/Greek-Salad-6-1.jpg',
      'price': 3.49
    },
    {
      'name': 'Onion Rings',
      'image': 'https://t3.ftcdn.net/jpg/00/58/75/74/360_F_58757430_FkzmzxBbNOYlkXwU31GozIAiNDMYS3YX.jpg',
      'price': 3.29
    },
    {
      'name': 'No Side',
      'image': 'https://media.hswstatic.com/eyJidWNrZXQiOiJjb250ZW50Lmhzd3N0YXRpYy5jb20iLCJrZXkiOiJnaWZcLzEwLXRoYW5rc2dpdm5nLW1lYWxzLW5vLW9uZS1jYXJlcy1hYm91dC0xLmpwZyIsImVkaXRzIjp7InJlc2l6ZSI6eyJ3aWR0aCI6MjUwfX19',
      'price': 0
    }
  ];
  Map<String, dynamic>? selectedSide;

  // Allergy Options
  final List<String> allergyOptions = ['Gluten Free', 'Dairy Free', 'Nut Free', 'Vegan', 'None'];
  List<String> selectedAllergies = [];

  // Additional Notes
  final TextEditingController notesController = TextEditingController();

  // Theme colors
  final Color bgColor = Color(0xFF121212);
  final Color surfaceColor = Color(0xFF1E1E1E);
  final Color primaryColor = Color(0xFF4CAF50);
  final Color accentColor = Color(0xFF8BC34A);
  final Color textColor = Color(0xFFE0E0E0);
  final Color secondaryTextColor = Color(0xFF9E9E9E);
  final Color cardColor = Color(0xFF252525);
  final Color dividerColor = Color(0xFF353535);

  @override
  Widget build(BuildContext context) {
    final totalPrice = widget.item.price + (selectedSide?['price'] ?? 0);
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Container(
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.5),
            blurRadius: 15,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildDragHandle(),
          Expanded(
            child: isLandscape 
              ? _buildLandscapeLayout(totalPrice)
              : _buildPortraitLayout(totalPrice),
          ),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(double totalPrice) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left side - Image and Description
        Expanded(
          flex: 3,
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(child: _buildHeader()),
              SliverToBoxAdapter(child: const SizedBox(height: 16)),
              SliverToBoxAdapter(child: _buildDescription()),
            ],
          ),
        ),
        // Vertical Divider
        Container(
          width: 1,
          margin: const EdgeInsets.symmetric(vertical: 16),
          color: dividerColor,
        ),
        // Right side - Customization Options
        Expanded(
          flex: 4,
          child: Column(
            children: [
              Expanded(
                child: CustomScrollView(
                  slivers: [
                    SliverToBoxAdapter(child: _buildCookingOptions()),
                    SliverToBoxAdapter(child: const SizedBox(height: 24)),
                    SliverToBoxAdapter(child: _buildSideOptions()),
                    SliverToBoxAdapter(child: const SizedBox(height: 24)),
                    SliverToBoxAdapter(child: _buildAllergyOptions()),
                    SliverToBoxAdapter(child: const SizedBox(height: 24)),
                    SliverToBoxAdapter(child: _buildNotesSection()),
                  ],
                ),
              ),
              _buildAddToCartButton(totalPrice),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPortraitLayout(double totalPrice) {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(child: _buildHeader()),
        SliverToBoxAdapter(child: const SizedBox(height: 16)),
        SliverToBoxAdapter(child: _buildDescription()),
        SliverToBoxAdapter(child: const SizedBox(height: 24)),
        SliverToBoxAdapter(child: _buildCookingOptions()),
        SliverToBoxAdapter(child: const SizedBox(height: 24)),
        SliverToBoxAdapter(child: _buildSideOptions()),
        SliverToBoxAdapter(child: const SizedBox(height: 24)),
        SliverToBoxAdapter(child: _buildAllergyOptions()),
        SliverToBoxAdapter(child: const SizedBox(height: 24)),
        SliverToBoxAdapter(child: _buildNotesSection()),
        SliverToBoxAdapter(child: const SizedBox(height: 100)),
        SliverToBoxAdapter(child: _buildAddToCartButton(totalPrice)),
      ],
    );
  }

  Widget _buildDragHandle() {
    return Container(
      width: 60,
      height: 6,
      margin: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade600,
        borderRadius: BorderRadius.circular(3),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 200,
      width: double.infinity,
      child: Stack(
        children: [
          // Food image with gradient overlay
          ClipRRect(
            borderRadius: BorderRadius.circular(0),
            child: Container(
              width: double.infinity,
              height: 200,
              child: ShaderMask(
                shaderCallback: (Rect bounds) {
                  return LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.transparent, Colors.black.withOpacity(0.9)],
                  ).createShader(bounds);
                },
                blendMode: BlendMode.darken,
                child: Image.network(
                  widget.item.image,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // Item name and price overlay
          Positioned(
            bottom: 16,
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.item.name,
                  style: GoogleFonts.poppins(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  '\$${widget.item.price.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 22,
                    fontWeight: FontWeight.w600,
                    color: accentColor,
                  ),
                ),
              ],
            ),
          ),

          // Close button
          Positioned(
            top: 12,
            right: 12,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
             widget.item.description,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: secondaryTextColor,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.access_time, size: 16, color: secondaryTextColor),
              const SizedBox(width: 4),
              Text(
                '${widget.item.prepTime} min',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: secondaryTextColor,
                ),
              ),
              const SizedBox(width: 12),
              Icon(Icons.local_fire_department, size: 16, color: Colors.orange),
              const SizedBox(width: 4),
              Text(
                '320 cal',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: secondaryTextColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCookingOptions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cooking Preference',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: cookingOptions.map((option) {
              final isSelected = selectedCookingOption == option;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedCookingOption = option;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    color: isSelected ? primaryColor.withOpacity(0.2) : surfaceColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? primaryColor : dividerColor,
                      width: 1.5,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isSelected)
                        Icon(
                          Icons.check_circle,
                          size: 18,
                          color: primaryColor,
                        ),
                      if (isSelected)
                        const SizedBox(width: 6),
                      Text(
                        option,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected ? primaryColor : textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSideOptions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose a Side',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 140,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: sideOptions.length,
              itemBuilder: (context, index) {
                final side = sideOptions[index];
                final isSelected = selectedSide == side;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedSide = side;
                    });
                  },
                  child: Container(
                    width: 120,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: cardColor,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: isSelected ? primaryColor : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Stack(
                          children: [
                            ClipRRect(
                              borderRadius: const BorderRadius.vertical(top: Radius.circular(14)),
                              child: Image.network(
                                side['image'],
                                height: 80,
                                width: double.infinity,
                                fit: BoxFit.cover,
                              ),
                            ),
                            if (isSelected)
                              Positioned(
                                top: 5,
                                right: 5,
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: primaryColor,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                side['name'],
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: textColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                '\$${side['price'].toStringAsFixed(2)}',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: accentColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAllergyOptions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Allergy Information',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: allergyOptions.map((allergy) {
              final isSelected = selectedAllergies.contains(allergy);
              return GestureDetector(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      selectedAllergies.remove(allergy);
                    } else {
                      selectedAllergies.add(allergy);
                    }
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? primaryColor.withOpacity(0.2) : surfaceColor,
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: isSelected ? primaryColor : dividerColor,
                      width: 1.5,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isSelected)
                        Icon(
                          Icons.check,
                          size: 16,
                          color: primaryColor,
                        ),
                      if (isSelected)
                        const SizedBox(width: 4),
                      Text(
                        allergy,
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected ? primaryColor : textColor,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Special Instructions',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: notesController,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: textColor,
            ),
            decoration: InputDecoration(
              hintText: 'Add any special instructions...',
              hintStyle: GoogleFonts.poppins(
                fontSize: 14,
                color: secondaryTextColor.withOpacity(0.7),
              ),
              filled: true,
              fillColor: surfaceColor,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: dividerColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: primaryColor, width: 1.5),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildAddToCartButton(double totalPrice) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: surfaceColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Total Price',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: secondaryTextColor,
                    ),
                  ),
                  Text(
                    '\$${totalPrice.toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: () {
                  // Create a detailed order object
                  final orderDetails = {
                    'type': 'customized',
                    'baseItemId': widget.item.id,
                    'cookingOption': selectedCookingOption,
                    'side': selectedSide != null ? {
                      'name': selectedSide!['name'],
                      'price': selectedSide!['price'],
                    } : null,
                    'allergies': selectedAllergies,
                    'notes': notesController.text.trim(),
                    'addedOn': DateTime.now().toIso8601String(),
                  };

                  // Add to cart with full details
                  context.read<POSBloc>().add(AddToCart(
                    widget.item,
                    id: widget.item.id,
                    name: widget.item.name,
                    price: totalPrice,
                    customization: orderDetails,
                  ));

                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.shopping_cart_outlined),
                    const SizedBox(width: 8),
                    Text(
                      'Add to Cart',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
