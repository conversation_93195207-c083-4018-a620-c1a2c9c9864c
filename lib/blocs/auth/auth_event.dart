import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class SignInRequested extends AuthEvent {
  final String emailAddress;
  final String password;
  final String employeeType;

  const SignInRequested(this.emailAddress, this.password, this.employeeType);
}

class SignOutRequested extends AuthEvent {
  final BuildContext? context;

  SignOutRequested({this.context});
}

class CheckAuthStatus extends AuthEvent {}

class TokenExpired extends AuthEvent {
  const TokenExpired();
}

class AuthErrorEvent extends AuthEvent {
  final String message;

  const AuthErrorEvent(this.message);

  @override
  List<Object> get props => [message];
}

class AuthLoadingEvent extends AuthEvent {}

class AuthAuthenticatedEvent extends AuthEvent {
  final String token;

  const AuthAuthenticatedEvent(this.token);

  @override
  List<Object> get props => [token];
}
