import 'dart:async';
import 'package:easydine_main/services/branch_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/staff_model.dart';
import '../../services/staff_service.dart';
import 'session_event.dart';
import 'session_state.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class SessionBloc extends Bloc<SessionEvent, SessionState> {
  static const sessionTimeout = Duration(minutes: 30);
  Timer? _sessionTimer;
  Timer? _activityTimer;

  final StaffService staffService = StaffService();
  List<StaffModel> staffList = [];

  SessionBloc() : super(const SessionState()) {
    on<InitializeSession>(_onInitializeSession);
    on<VerifyPin>(_onVerifyPin);
    on<CheckSessionStatus>(_onCheckSessionStatus);
    on<EndSession>(_onEndSession);
    on<UpdateLastActive>(_onUpdateLastActive);

    _activityTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => add(CheckSessionStatus()),
    );
  }

  Future<void> _onInitializeSession(
    InitializeSession event,
    Emitter<SessionState> emit,
  ) async {
    debugPrint('🚀 SessionBloc: InitializeSession called for pin entry page');
    debugPrint(
        '🚀 SessionBloc: Manager has authenticated, fetching staff with manager token');

    // Emit loading state
    emit(state.copyWith(
      status: SessionStatus.loadingStaff,
      waiterId: event.waiterId,
      waiterName: event.waiterName,
    ));

    // Get the selected branch ID (set by manager during branch selection)
    final branchId = await BranchService.getSelectedBranchId();
    debugPrint('🏢 SessionBloc: Using branch ID: $branchId');

    if (branchId == null || branchId.isEmpty) {
      debugPrint('❌ SessionBloc: No branch selected');
      emit(state.copyWith(
        status: SessionStatus.error,
        error: 'No branch selected. Please select a branch first.',
      ));
      return;
    }

    try {
      debugPrint(
          '📋 SessionBloc: Fetching staff using manager\'s token via HTTP interceptor...');

      // Fetch all staff and clocked-in staff using manager's token
      // The HTTP interceptor will automatically add the manager's token to these requests
      staffList = await staffService.fetchBranchStaffsWithCache(branchId);
      final clockedInStaff =
          await staffService.fetchBranchClockedInStaffs(branchId);

      debugPrint(
          '✅ SessionBloc: Successfully loaded ${staffList.length} total staff');
      debugPrint(
          '✅ SessionBloc: Found ${clockedInStaff.length} clocked-in staff');

      // Emit pin required state - staff data will be handled by StaffBloc
      emit(state.copyWith(
        status: SessionStatus.pinRequired,
        waiterId: event.waiterId,
        waiterName: event.waiterName,
      ));

      debugPrint('✅ SessionBloc: Pin entry page ready with staff data');
    } catch (e) {
      debugPrint('❌ SessionBloc: Error fetching staff: $e');
      emit(state.copyWith(
        status: SessionStatus.error,
        error: 'Failed to load staff data: ${e.toString()}',
        waiterId: event.waiterId,
        waiterName: event.waiterName,
      ));
    }
  }

  Future<void> _onVerifyPin(
    VerifyPin event,
    Emitter<SessionState> emit,
  ) async {
    emit(state.copyWith(status: SessionStatus.loadingStaff));
    try {
      debugPrint(
          '🔄 SessionBloc: Verifying PIN for staff ${event.staffId} (dashboard access)');

      // Check token before making the request
      final prefs = await SharedPreferences.getInstance();
      final tokenBefore = prefs.getString('access-token');
      debugPrint(
          '🔍 SessionBloc: Token before PIN verification: ${tokenBefore != null ? 'EXISTS' : 'NULL'}');

      // Verify the PIN for dashboard access (smart verification)
      final staff = await staffService.verifyStaffPin(event.staffId, event.pin);

      // Check token after PIN verification (should remain unchanged)
      final tokenAfter = prefs.getString('access-token');
      debugPrint(
          '🔍 SessionBloc: Token after PIN verification: ${tokenAfter != null ? 'EXISTS' : 'NULL'}');

      if (tokenBefore != null && tokenAfter == null) {
        debugPrint(
            '⚠️ SessionBloc: TOKEN WAS CLEARED during PIN verification!');
      } else if (tokenBefore != null && tokenAfter != null) {
        debugPrint(
            '✅ SessionBloc: Manager token preserved during PIN verification');
      }

      if (staff != null) {
        debugPrint(
            '✅ SessionBloc: PIN verification successful, creating session');
        debugPrint('✅ SessionBloc: Staff data received: ${staff.name}');

        // Create session for the staff member using the returned staff data
        await _saveSessionData(staff.id, staff.name);

        emit(state.copyWith(
          status: SessionStatus.authenticated,
          waiterId: staff.id,
          waiterName: staff.name,
          lastActiveTime: DateTime.now(),
        ));

        _startSessionTimer();

        debugPrint('✅ SessionBloc: Session created for ${staff.name}');
      } else {
        debugPrint('❌ SessionBloc: PIN verification failed');
        emit(state.copyWith(
          status: SessionStatus.error,
          error: 'Invalid PIN. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ SessionBloc: PIN verification error: $e');
      emit(state.copyWith(
        status: SessionStatus.error,
        error: 'Failed to verify PIN: ${e.toString()}',
      ));
    }
  }

  Future<void> _onCheckSessionStatus(
    CheckSessionStatus event,
    Emitter<SessionState> emit,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final lastActiveStr = prefs.getString('lastActiveTime');

    if (lastActiveStr != null) {
      final lastActive = DateTime.parse(lastActiveStr);
      final now = DateTime.now();
      final difference = now.difference(lastActive);

      if (difference >= sessionTimeout) {
        await _clearSessionData();
        emit(state.copyWith(status: SessionStatus.pinRequired));
      }
    }
  }

  Future<void> _onEndSession(
    EndSession event,
    Emitter<SessionState> emit,
  ) async {
    await _clearSessionData();
    emit(state.copyWith(status: SessionStatus.pinRequired));
  }

  Future<void> _onUpdateLastActive(
    UpdateLastActive event,
    Emitter<SessionState> emit,
  ) async {
    if (state.status == SessionStatus.authenticated) {
      final now = DateTime.now();
      emit(state.copyWith(lastActiveTime: now));
      _startSessionTimer();
    }
  }

  Future<void> _saveSessionData(String waiterId, String waiterName) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('waiterId', waiterId);
    await prefs.setString('waiterName', waiterName);
    await prefs.setString('lastActiveTime', DateTime.now().toIso8601String());
  }

  Future<void> _clearSessionData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('lastActiveTime');
    await prefs.remove('waiterId');
    await prefs.remove('waiterName');
  }

  void _startSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = Timer(sessionTimeout, () {
      add(EndSession());
    });
  }

  @override
  Future<void> close() {
    _sessionTimer?.cancel();
    _activityTimer?.cancel();
    return super.close();
  }
}
