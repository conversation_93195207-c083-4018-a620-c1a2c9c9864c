import 'dart:async';
import 'package:easydine_main/services/branch_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../models/staff_model.dart';
import '../../services/staff_service.dart';
import 'session_event.dart';
import 'session_state.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class SessionBloc extends Bloc<SessionEvent, SessionState> {
  static const sessionTimeout = Duration(minutes: 30);
  Timer? _sessionTimer;
  Timer? _activityTimer;

  final StaffService staffService = StaffService();
  List<StaffModel> staffList = [];

  SessionBloc() : super(const SessionState()) {
    on<InitializeSession>(_onInitializeSession);
    on<CheckSessionStatus>(_onCheckSessionStatus);
    on<EndSession>(_onEndSession);
    on<UpdateLastActive>(_onUpdateLastActive);
    on<VerifyPin>(_onVerifyPin);

    _activityTimer = Timer.periodic(
      const Duration(minutes: 1),
      (_) => add(CheckSessionStatus()),
    );
  }

  Future<void> _onInitializeSession(
    InitializeSession event,
    Emitter<SessionState> emit,
  ) async {
    debugPrint('🚀 SessionBloc: InitializeSession called for pin entry page');
    debugPrint(
        '🚀 SessionBloc: Manager has authenticated, fetching staff with manager token');

    // Emit loading state
    emit(state.copyWith(
      status: SessionStatus.loadingStaff,
      waiterId: event.waiterId,
      waiterName: event.waiterName,
    ));

    // Get the selected branch ID (set by manager during branch selection)
    final branchId = await BranchService.getSelectedBranchId();
    debugPrint('🏢 SessionBloc: Using branch ID: $branchId');

    if (branchId == null || branchId.isEmpty) {
      debugPrint('❌ SessionBloc: No branch selected');
      emit(state.copyWith(
        status: SessionStatus.error,
        error: 'No branch selected. Please select a branch first.',
      ));
      return;
    }

    try {
      debugPrint(
          '📋 SessionBloc: Fetching staff using manager\'s token via HTTP interceptor...');

      // Fetch all staff and clocked-in staff using manager's token
      // The HTTP interceptor will automatically add the manager's token to these requests
      staffList = await staffService.fetchBranchStaffs(branchId);
      final clockedInStaff =
          await staffService.fetchBranchClockedInStaffs(branchId);

      debugPrint(
          '✅ SessionBloc: Successfully loaded ${staffList.length} total staff');
      debugPrint(
          '✅ SessionBloc: Found ${clockedInStaff.length} clocked-in staff');

      // Emit pin required state - staff data will be handled by StaffBloc
      emit(state.copyWith(
        status: SessionStatus.pinRequired,
        waiterId: event.waiterId,
        waiterName: event.waiterName,
      ));

      debugPrint('✅ SessionBloc: Pin entry page ready with staff data');
    } catch (e) {
      debugPrint('❌ SessionBloc: Error fetching staff: $e');
      emit(state.copyWith(
        status: SessionStatus.error,
        error: 'Failed to load staff data: ${e.toString()}',
        waiterId: event.waiterId,
        waiterName: event.waiterName,
      ));
    }
  }

  Future<void> _onCheckSessionStatus(
    CheckSessionStatus event,
    Emitter<SessionState> emit,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final lastActiveStr = prefs.getString('lastActiveTime');

    if (lastActiveStr != null) {
      final lastActive = DateTime.parse(lastActiveStr);
      final now = DateTime.now();
      final difference = now.difference(lastActive);

      if (difference >= sessionTimeout) {
        await _clearSessionData();
        emit(state.copyWith(status: SessionStatus.pinRequired));
      }
    }
  }

  Future<void> _onEndSession(
    EndSession event,
    Emitter<SessionState> emit,
  ) async {
    await _clearSessionData();
    emit(state.copyWith(status: SessionStatus.pinRequired));
  }

  Future<void> _onUpdateLastActive(
    UpdateLastActive event,
    Emitter<SessionState> emit,
  ) async {
    if (state.status == SessionStatus.authenticated) {
      final now = DateTime.now();
      emit(state.copyWith(lastActiveTime: now));
      _startSessionTimer();
    }
  }

  Future<void> _onVerifyPin(
    VerifyPin event,
    Emitter<SessionState> emit,
  ) async {
    emit(state.copyWith(status: SessionStatus.loadingStaff));
    try {
      print('🔄 SessionBloc: Verifying PIN for staff ${event.staffId}');

      // First verify the PIN by attempting check-in
      final success = await staffService.checkInStaff(event.staffId, event.pin);

      if (success) {
        print('✅ SessionBloc: PIN verification successful, creating session');

        // Find the staff member from the current staff list
        final staff = staffList.firstWhere(
          (s) => s.id == event.staffId,
          orElse: () => StaffModel(
            id: event.staffId,
            firstName: 'Staff',
            lastName: 'Member',
            emailAddress: '',
            phoneNumber: '',
            address: '',
            pin: int.tryParse(event.pin),
            profileUrl: '',
            branches: [],
            staffDaysAvailable: StaffDaysAvailable(days: {}),
            defaultShiftTiming: DefaultShiftTiming(weeklyTimings: {}),
            staffCertifications: [],
            role: 'Staff',
          ),
        );

        // Create session for the staff member
        await _saveSessionData(event.staffId, staff.name);

        emit(state.copyWith(
          status: SessionStatus.authenticated,
          waiterId: event.staffId,
          waiterName: staff.name,
          currentStaff: staff,
          lastActiveTime: DateTime.now(),
        ));

        // Staff list refresh will be handled by StaffBloc

        print('✅ SessionBloc: Session created for ${staff.name}');
      } else {
        print('❌ SessionBloc: PIN verification failed');
        emit(state.copyWith(
          status: SessionStatus.error,
          error: 'Invalid PIN. Please try again.',
        ));
      }
    } catch (e) {
      print('❌ SessionBloc: PIN verification error: $e');
      emit(state.copyWith(
        status: SessionStatus.error,
        error: 'Failed to verify PIN: ${e.toString()}',
      ));
    }
  }

  Future<void> _saveSessionData(String waiterId, String waiterName) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('waiterId', waiterId);
    await prefs.setString('waiterName', waiterName);
    await prefs.setString('lastActiveTime', DateTime.now().toIso8601String());
  }

  Future<void> _clearSessionData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('lastActiveTime');
    await prefs.remove('waiterId');
    await prefs.remove('waiterName');
  }

  void _startSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = Timer(sessionTimeout, () {
      add(EndSession());
    });
  }

  @override
  Future<void> close() {
    _sessionTimer?.cancel();
    _activityTimer?.cancel();
    return super.close();
  }
}
