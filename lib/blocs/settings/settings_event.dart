// settings_event.dart
part of 'settings_bloc.dart';

abstract class SettingsEvent {}

class ToggleTakeaway extends SettingsEvent {
  final bool isEnabled;
  ToggleTakeaway({required this.isEnabled});
}

class ToggleDelivery extends SettingsEvent {
  final bool isEnabled;
  ToggleDelivery({required this.isEnabled});
}

class ToggleRushOrder extends SettingsEvent {
  final bool isEnabled;
  ToggleRushOrder({required this.isEnabled});
}

class ToggleAutoGenerateOrderId extends SettingsEvent {
  final bool isEnabled;
  ToggleAutoGenerateOrderId({required this.isEnabled});
}

class ToggleContactlessDineIn extends SettingsEvent {
  final bool isEnabled;
  ToggleContactlessDineIn({required this.isEnabled});
}

class UpdateQrOrderSettings extends SettingsEvent {
  final bool? autoAccept;
  final bool? tableVerification;
  final String? sound;

  UpdateQrOrderSettings({
    this.autoAccept,
    this.tableVerification,
    this.sound,
  });
}

class UpdateRushOrderSettings extends SettingsEvent {
  final double? fee;
  final int? priority;
  final int? maxTime;

  UpdateRushOrderSettings({
    this.fee,
    this.priority,
    this.maxTime,
  });
}
