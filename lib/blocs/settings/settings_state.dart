// settings_state.dart
part of 'settings_bloc.dart';

class SettingsState {
  final bool takeawayEnabled;
  final bool deliveryEnabled;
  final bool rushOrderEnabled;
  final bool autoGenerateOrderId;
  final bool contactlessDineInEnabled;
  
  // New fields for QR ordering
  final bool? autoAcceptQrOrders;
  final bool? requireTableVerification;
  final String? qrOrderSound;
  
  // New fields for rush orders
  final double? rushOrderFee;
  final int? rushOrderPriority;
  final int? rushOrderMaxTime;

  SettingsState({
    required this.takeawayEnabled,
    required this.deliveryEnabled,
    required this.rushOrderEnabled,
    required this.autoGenerateOrderId,
    required this.contactlessDineInEnabled,
    this.autoAcceptQrOrders,
    this.requireTableVerification,
    this.qrOrderSound,
    this.rushOrderFee,
    this.rushOrderPriority,
    this.rushOrderMaxTime,
  });

  factory SettingsState.initial() {
    return SettingsState(
      takeawayEnabled: true,
      deliveryEnabled: true,
      rushOrderEnabled: false,
      autoGenerateOrderId: false,
      contactlessDineInEnabled: false,
    );
  }

  SettingsState copyWith({
    bool? takeawayEnabled,
    bool? deliveryEnabled,
    bool? rushOrderEnabled,
    bool? autoGenerateOrderId,
    bool? contactlessDineInEnabled,
    bool? autoAcceptQrOrders,
    bool? requireTableVerification,
    String? qrOrderSound,
    double? rushOrderFee,
    int? rushOrderPriority,
    int? rushOrderMaxTime,
  }) {
    return SettingsState(
      takeawayEnabled: takeawayEnabled ?? this.takeawayEnabled,
      deliveryEnabled: deliveryEnabled ?? this.deliveryEnabled,
      rushOrderEnabled: rushOrderEnabled ?? this.rushOrderEnabled,
      autoGenerateOrderId: autoGenerateOrderId ?? this.autoGenerateOrderId,
      contactlessDineInEnabled: contactlessDineInEnabled ?? this.contactlessDineInEnabled,
      autoAcceptQrOrders: autoAcceptQrOrders ?? this.autoAcceptQrOrders,
      requireTableVerification: requireTableVerification ?? this.requireTableVerification,
      qrOrderSound: qrOrderSound ?? this.qrOrderSound,
      rushOrderFee: rushOrderFee ?? this.rushOrderFee,
      rushOrderPriority: rushOrderPriority ?? this.rushOrderPriority,
      rushOrderMaxTime: rushOrderMaxTime ?? this.rushOrderMaxTime,
    );
  }
}
