import 'package:equatable/equatable.dart';

enum AttendanceStatus {
  initial,
  loading,
  success,
  error,
}

enum AttendanceOperation {
  none,
  clockIn,
  clockOut,
  checkIn,
  checkOut,
}

class AttendanceState extends Equatable {
  final AttendanceStatus status;
  final AttendanceOperation lastOperation;
  final String? error;
  final String? successMessage;

  const AttendanceState({
    this.status = AttendanceStatus.initial,
    this.lastOperation = AttendanceOperation.none,
    this.error,
    this.successMessage,
  });

  AttendanceState copyWith({
    AttendanceStatus? status,
    AttendanceOperation? lastOperation,
    String? error,
    String? successMessage,
  }) {
    return AttendanceState(
      status: status ?? this.status,
      lastOperation: lastOperation ?? this.lastOperation,
      error: error,
      successMessage: successMessage,
    );
  }

  @override
  List<Object?> get props => [
        status,
        lastOperation,
        error,
        successMessage,
      ];
}
