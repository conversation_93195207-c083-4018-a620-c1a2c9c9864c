import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../services/staff_service.dart';
import 'attendance_event.dart';
import 'attendance_state.dart';

class AttendanceBloc extends Bloc<AttendanceEvent, AttendanceState> {
  final StaffService _staffService = StaffService();

  AttendanceBloc() : super(const AttendanceState()) {
    on<ClockInStaff>(_onClockInStaff);
    on<ClockOutStaff>(_onClockOutStaff);
    on<CheckInStaff>(_onCheckInStaff);
    on<CheckOutStaff>(_onCheckOutStaff);
  }

  Future<void> _onClockInStaff(
    ClockInStaff event,
    Emitter<AttendanceState> emit,
  ) async {
    emit(state.copyWith(
      status: AttendanceStatus.loading,
      lastOperation: AttendanceOperation.clockIn,
    ));

    try {
      debugPrint('🔄 AttendanceBloc: Clock-in staff ${event.staffId}');
      
      final success = await _staffService.clockInStaff(event.staffId, event.pin);

      if (success) {
        debugPrint('✅ AttendanceBloc: Clock-in successful');
        emit(state.copyWith(
          status: AttendanceStatus.success,
          successMessage: 'Staff clocked in successfully',
        ));
      } else {
        debugPrint('❌ AttendanceBloc: Clock-in failed');
        emit(state.copyWith(
          status: AttendanceStatus.error,
          error: 'Clock-in failed. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ AttendanceBloc: Clock-in error: $e');
      emit(state.copyWith(
        status: AttendanceStatus.error,
        error: 'Failed to clock in: ${e.toString()}',
      ));
    }
  }

  Future<void> _onClockOutStaff(
    ClockOutStaff event,
    Emitter<AttendanceState> emit,
  ) async {
    emit(state.copyWith(
      status: AttendanceStatus.loading,
      lastOperation: AttendanceOperation.clockOut,
    ));

    try {
      debugPrint('🔄 AttendanceBloc: Clock-out staff ${event.staffId}');
      
      final success = await _staffService.clockOutStaff(event.staffId, event.pin);

      if (success) {
        debugPrint('✅ AttendanceBloc: Clock-out successful');
        emit(state.copyWith(
          status: AttendanceStatus.success,
          successMessage: 'Staff clocked out successfully',
        ));
      } else {
        debugPrint('❌ AttendanceBloc: Clock-out failed');
        emit(state.copyWith(
          status: AttendanceStatus.error,
          error: 'Clock-out failed. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ AttendanceBloc: Clock-out error: $e');
      emit(state.copyWith(
        status: AttendanceStatus.error,
        error: 'Failed to clock out: ${e.toString()}',
      ));
    }
  }

  Future<void> _onCheckInStaff(
    CheckInStaff event,
    Emitter<AttendanceState> emit,
  ) async {
    emit(state.copyWith(
      status: AttendanceStatus.loading,
      lastOperation: AttendanceOperation.checkIn,
    ));

    try {
      debugPrint('🔄 AttendanceBloc: Check-in staff ${event.staffId}');
      
      final success = await _staffService.checkInStaff(event.staffId, event.pin);

      if (success) {
        debugPrint('✅ AttendanceBloc: Check-in successful');
        emit(state.copyWith(
          status: AttendanceStatus.success,
          successMessage: 'Staff checked in successfully',
        ));
      } else {
        debugPrint('❌ AttendanceBloc: Check-in failed');
        emit(state.copyWith(
          status: AttendanceStatus.error,
          error: 'Check-in failed. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ AttendanceBloc: Check-in error: $e');
      emit(state.copyWith(
        status: AttendanceStatus.error,
        error: 'Failed to check in: ${e.toString()}',
      ));
    }
  }

  Future<void> _onCheckOutStaff(
    CheckOutStaff event,
    Emitter<AttendanceState> emit,
  ) async {
    emit(state.copyWith(
      status: AttendanceStatus.loading,
      lastOperation: AttendanceOperation.checkOut,
    ));

    try {
      debugPrint('🔄 AttendanceBloc: Check-out staff ${event.staffId}');
      
      final success = await _staffService.checkOutStaff(event.staffId, event.pin);

      if (success) {
        debugPrint('✅ AttendanceBloc: Check-out successful');
        emit(state.copyWith(
          status: AttendanceStatus.success,
          successMessage: 'Staff checked out successfully',
        ));
      } else {
        debugPrint('❌ AttendanceBloc: Check-out failed');
        emit(state.copyWith(
          status: AttendanceStatus.error,
          error: 'Check-out failed. Please try again.',
        ));
      }
    } catch (e) {
      debugPrint('❌ AttendanceBloc: Check-out error: $e');
      emit(state.copyWith(
        status: AttendanceStatus.error,
        error: 'Failed to check out: ${e.toString()}',
      ));
    }
  }
}
