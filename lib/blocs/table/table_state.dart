import 'package:equatable/equatable.dart';
import '../../models/table_model.dart';
import '../../models/order_model.dart';

class TableState extends Equatable {
  final List<TableModel> tables;
  final bool isLoading;
  final String? error;
  final String? filterStatus;
  final int? filterSeats;
  final List<int> availableFloors;
  final int currentFloor;
  final OrderModel? currentOrder;

  const TableState({
    this.tables = const [],
    this.isLoading = false,
    this.error,
    this.filterStatus,
    this.filterSeats,
    this.availableFloors = const [1],
    this.currentFloor = 1,
    this.currentOrder,
  });

  TableState copyWith({
    List<TableModel>? tables,
    bool? isLoading,
    String? error,
    String? filterStatus,
    int? filterSeats,
    List<int>? availableFloors,
    int? currentFloor,
    OrderModel? currentOrder,
  }) {
    return TableState(
      tables: tables ?? this.tables,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      filterStatus: filterStatus ?? this.filterStatus,
      filterSeats: filterSeats ?? this.filterSeats,
      availableFloors: availableFloors ?? this.availableFloors,
      currentFloor: currentFloor ?? this.currentFloor,
      currentOrder: currentOrder ?? this.currentOrder,
    );
  }

  @override
  List<Object?> get props => [
        tables,
        isLoading,
        error,
        filterStatus,
        filterSeats,
        availableFloors,
        currentFloor,
        currentOrder,
      ];
}
