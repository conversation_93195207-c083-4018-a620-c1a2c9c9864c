import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../models/table_model.dart';
import '../../models/order_model.dart';
import '../running_orders/running_orders_bloc.dart';
import 'table_event.dart';
import 'table_state.dart';

class TableBloc extends Bloc<TableEvent, TableState> {
  final RunningOrdersBloc runningOrdersBloc;

  TableBloc({required this.runningOrdersBloc}) : super(const TableState()) {
    on<LoadTables>(_onLoadTables);
    on<UpdateTableStatus>(_onUpdateTableStatus);
    on<UpdateTableCleaningStatus>(_onUpdateTableCleaningStatus);
    on<UpdateTableFilters>(_onUpdateTableFilters);
    on<UpdateCurrentFloor>(_onUpdateCurrentFloor);
    on<ViewTableBill>(_onViewTableBill);
    on<ReserveTable>(_onReserveTable);
    on<CancelReservation>(_onCancelReservation);
    on<MarkTableAsOccupied>(_onMarkTableAsOccupied);
    on<MarkTableAsAvailable>(_onMarkTableAsAvailable);
    on<UpdateTableLayout>(_onUpdateTableLayout);
  }

  Future<void> _onLoadTables(LoadTables event, Emitter<TableState> emit) async {
    emit(state.copyWith(isLoading: true));
    try {
      // In production, replace this with actual API call
      final tables = _getMockTables();
      final availableFloors = _getAvailableFloors(tables);
      
      emit(state.copyWith(
        tables: tables,
        availableFloors: availableFloors,
        isLoading: false,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        error: 'Failed to load tables: ${e.toString()}',
        isLoading: false,
      ));
    }
  }

  List<TableModel> _getMockTables() {
    // Return your mock tables list here
    // You can move the existing table definitions here
    return [
            TableModel(
        id: 1,
        seats: 2,
        status: 'Available',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 1,
        mainAxisCellCount: 1,
        location: 'Near Window',
        price: 0.0,
        minimumSpend: 50.0,
        reservationFee: 10.0,
        features: ['Romantic View', 'Sunlight Exposure'],
        reservationTime: null,
        reservedBy: null,
        reservationDetails: null,
        lastCleaned: null,
        lastOccupied: null,
        averageOccupancyTime: 90,
        popularityScore: 4.5,
        section: 'Main Floor',
        tableNumber: 'T1',
        floor: 1,
      ),
      TableModel(
        id: 2,
        seats: 4,
        status: 'Occupied',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'Center',
        price: 0.0,
        minimumSpend: 100.0,
        reservationFee: 15.0,
        features: ['Family Friendly', 'Near Kitchen'],
        lastCleaned: DateTime.now().subtract(Duration(hours: 2)),
        lastOccupied: DateTime.now(),
        averageOccupancyTime: 120,
        popularityScore: 4.0,
        section: 'Main Floor',
        tableNumber: 'T2',
        floor: 1,
      ),
      TableModel(
        id: 3,
        seats: 6,
        status: 'Reserved',
        cleaningStatus: 'Needs Cleaning',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'Outdoor Patio',
        price: 0.0,
        minimumSpend: 150.0,
        reservationFee: 20.0,
        features: ['Outdoor Seating', 'Heater Available'],
        reservationTime: DateTime.now().add(Duration(hours: 2)),
        reservedBy: 'John Doe',
        reservationDetails: {
          'name': 'John Doe',
          'phone': '555-0123',
          'time': '7:00 PM',
          'guests': '5',
          'notes': 'Birthday celebration',
          'reservationId': 'RES123',
          'deposit': 20.0,
          'specialRequests': ['Birthday Cake', 'Window Seat']
        },
        lastCleaned: DateTime.now().subtract(Duration(hours: 1)),
        lastOccupied: DateTime.now().subtract(Duration(minutes: 30)),
        averageOccupancyTime: 150,
        popularityScore: 4.8,
        section: 'Patio',
        tableNumber: 'P1',
        floor: 1,
      ),
      TableModel(
        id: 4,
        seats: 2,
        status: 'Available',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 1,
        mainAxisCellCount: 1,
        location: 'Corner',
        price: 0.0,
        minimumSpend: 50.0,
        reservationFee: 10.0,
        features: ['Quiet Spot', 'Ideal for Reading'],
        reservationTime: null,
        reservedBy: null,
        reservationDetails: null,
        lastCleaned: null,
        lastOccupied: null,
        averageOccupancyTime: 60,
        popularityScore: 4.2,
        section: 'Main Floor',
        tableNumber: 'T4',
        floor: 1,
      ),
      TableModel(
        id: 5,
        seats: 8,
        status: 'Available',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'VIP Section',
        price: 0.0,
        minimumSpend: 200.0,
        reservationFee: 30.0,
        features: ['Privacy', 'Power Outlets', 'Spacious Seating'],
        reservationTime: null,
        reservedBy: null,
        reservationDetails: null,
        lastCleaned: null,
        lastOccupied: null,
        averageOccupancyTime: 180,
        popularityScore: 4.9,
        section: 'VIP',
        tableNumber: 'VIP1',
        floor: 1,
      ),
      TableModel(
        id: 6,
        seats: 4,
        status: 'Occupied',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'Near Kitchen',
        price: 0.0,
        minimumSpend: 100.0,
        reservationFee: 15.0,
        features: ['Quick Service', 'Family-Friendly'],
        reservationTime: null,
        reservedBy: null,
        reservationDetails: null,
        lastCleaned: DateTime.now().subtract(Duration(hours: 1)),
        lastOccupied: DateTime.now(),
        averageOccupancyTime: 90,
        popularityScore: 4.3,
        section: 'Main Floor',
        tableNumber: 'T6',
        floor: 1,
      ),
      TableModel(
        id: 7,
        seats: 4,
        status: 'Reserved',
        cleaningStatus: 'Needs Cleaning',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'Terrace',
        price: 0.0,
        minimumSpend: 100.0,
        reservationFee: 15.0,
        features: ['Pet-Friendly', 'Outdoor Seating', 'Scenic View'],
        reservationTime: DateTime.now().add(Duration(hours: 1)),
        reservedBy: 'Jane Smith',
        reservationDetails: {
          'name': 'Jane Smith',
          'phone': '555-0124',
          'time': '6:30 PM',
          'guests': '4',
          'notes': 'Anniversary dinner',
          'reservationId': 'RES124',
          'deposit': 15.0,
          'specialRequests': ['Champagne', 'Flowers']
        },
        lastCleaned: DateTime.now().subtract(Duration(hours: 2)),
        lastOccupied: DateTime.now().subtract(Duration(hours: 1)),
        averageOccupancyTime: 120,
        popularityScore: 4.6,
        section: 'Terrace',
        tableNumber: 'T7',
        floor: 1,
      ),
      TableModel(
        id: 8,
        seats: 2,
        status: 'Available',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 1,
        mainAxisCellCount: 1,
        location: 'Near Bar',
        price: 0.0,
        minimumSpend: 50.0,
        reservationFee: 10.0,
        features: ['Bar Access', 'Quick Service'],
        reservationTime: null,
        reservedBy: null,
        reservationDetails: null,
        lastCleaned: null,
        lastOccupied: null,
        averageOccupancyTime: 30,
        popularityScore: 4.1,
        section: 'Bar Area',
        tableNumber: 'B1',
        floor: 2,
      ),
      TableModel(
        id: 9,
        seats: 6,
        status: 'Occupied',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'Near Stage',
        price: 0.0,
        minimumSpend: 150.0,
        reservationFee: 20.0,
        features: ['Live Music', 'Dance Floor'],
        reservationTime: null,
        reservedBy: null,
        reservationDetails: null,
        lastCleaned: DateTime.now().subtract(Duration(hours: 1)),
        lastOccupied: DateTime.now(),
        averageOccupancyTime: 90,
        popularityScore: 4.4,
        section: 'Main Floor',
        tableNumber: 'T9',
        floor: 2,
      ),
      TableModel(
        id: 10,
        seats: 4,
        status: 'Reserved',
        cleaningStatus: 'Needs Cleaning',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'Private Room',
        price: 0.0,
        minimumSpend: 200.0,
        reservationFee: 30.0,
        features: ['Private', 'Sound System'],
        reservationTime: DateTime.now().add(Duration(hours: 2)),
        reservedBy: 'Alice Johnson',
        reservationDetails: {
          'name': 'Alice Johnson',
          'phone': '555-0125',
          'time': '8:00 PM',
          'guests': '6',
          'notes': 'Business meeting',
          'reservationId': 'RES125',
          'deposit': 25.0,
          'specialRequests': ['Projector', 'Whiteboard']
        },
        lastCleaned: DateTime.now().subtract(Duration(hours: 3)),
        lastOccupied: DateTime.now().subtract(Duration(minutes: 45)),
        averageOccupancyTime: 150,
        popularityScore: 4.7,
        section: 'Private Room',
        tableNumber: 'PR1',
        floor: 2,
      ),
      TableModel(
        id: 11,
        seats: 2,
        status: 'Available',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 1,
        mainAxisCellCount: 1,
        location: 'Near Restroom',
        price: 0.0,
        minimumSpend: 50.0,
        reservationFee: 10.0,
        features: ['Convenient Location'],
        reservationTime: null,
        reservedBy: null,
        reservationDetails: null,
        lastCleaned: null,
        lastOccupied: null,
        averageOccupancyTime: 60,
        popularityScore: 4.0,
        section: 'Main Floor',
        tableNumber: 'T11',
        floor: 2,
      ),
      TableModel(
        id: 12,
        seats: 4,
        status: 'Occupied',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'Near Entrance',
        price: 0.0,
        minimumSpend: 100.0,
        reservationFee: 15.0,
        features: ['Easy Access', 'Good Visibility'],
        reservationTime: null,
        reservedBy: null,
        reservationDetails: null,
        lastCleaned: DateTime.now().subtract(Duration(hours: 1)),
        lastOccupied: DateTime.now(),
        averageOccupancyTime: 90,
        popularityScore: 4.3,
        section: 'Main Floor',
        tableNumber: 'T12',
        floor: 2,
      ),
      TableModel(
        id: 13,
        seats: 6,
        status: 'Reserved',
        cleaningStatus: 'Needs Cleaning',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'Near Exit',
        price: 0.0,
        minimumSpend: 150.0,
        reservationFee: 20.0,
        features: ['Quick Exit', 'Good for Groups'],
        reservationTime: DateTime.now().add(Duration(hours: 1)),
        reservedBy: 'Bob Brown',
        reservationDetails: {
          'name': 'Bob Brown',
          'phone': '555-0126',
          'time': '7:30 PM',
          'guests': '5',
          'notes': 'Friends gathering',
          'reservationId': 'RES126',
          'deposit': 20.0,
          'specialRequests': ['Extra Chairs']
        },
        lastCleaned: DateTime.now().subtract(Duration(hours: 2)),
        lastOccupied: DateTime.now().subtract(Duration(hours: 1)),
        averageOccupancyTime: 120,
        popularityScore: 4.6,
        section: 'Main Floor',
        tableNumber: 'T13',
        floor: 3,
      ),
      TableModel(
        id: 14,
        seats: 8,
        status: 'Available',
        cleaningStatus: 'Clean',
        crossAxisCellCount: 2,
        mainAxisCellCount: 1,
        location: 'Near Bar',
        price: 0.0,
        minimumSpend: 200.0,
        reservationFee: 30.0,
        features: ['Bar Access', 'Good for Large Groups'],
        reservationTime: null,
        reservedBy: null,
        reservationDetails: null,
        lastCleaned: null,
        lastOccupied: null,
        averageOccupancyTime: 180,
        popularityScore: 4.9,
        section: 'Bar Area',
        tableNumber: 'B2',
        floor: 3,
      ),
    ];
  }

  OrderModel? _getOrderForTable(String tableId) {
    try {
      // This is a mock implementation. Replace with actual implementation
      // that integrates with your RunningOrdersBloc
      return null;
    } catch (e) {
      return null;
    }
  }

  void _onUpdateTableStatus(UpdateTableStatus event, Emitter<TableState> emit) {
    try {
      final updatedTables = state.tables.map((table) {
        if (table.id == event.tableId) {
          return table.copyWith(
            status: event.newStatus,
          );
        }
        return table;
      }).toList();

      emit(state.copyWith(
        tables: updatedTables,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update table status: ${e.toString()}'));
    }
  }

  void _onUpdateTableCleaningStatus(
    UpdateTableCleaningStatus event,
    Emitter<TableState> emit,
  ) {
    try {
      final updatedTables = state.tables.map((table) {
        if (table.id == event.tableId) {
          return table.copyWith(
            cleaningStatus: event.newStatus,
            lastCleaned: event.newStatus == 'Clean' ? DateTime.now() : table.lastCleaned,
          );
        }
        return table;
      }).toList();

      emit(state.copyWith(
        tables: updatedTables,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update cleaning status: ${e.toString()}'));
    }
  }

  void _onReserveTable(ReserveTable event, Emitter<TableState> emit) {
    try {
      final table = state.tables.firstWhere((t) => t.id == event.tableId);
      
      if (table.status != 'Available') {
        emit(state.copyWith(error: 'Table is not available for reservation'));
        return;
      }

      final updatedTables = state.tables.map((t) {
        if (t.id == event.tableId) {
          return t.copyWith(
            status: 'Reserved',
            reservationDetails: event.reservationDetails,
            reservationTime: DateTime.parse(event.reservationDetails['time']),
            reservedBy: event.reservationDetails['name'],
          );
        }
        return t;
      }).toList();

      emit(state.copyWith(
        tables: updatedTables,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to reserve table: ${e.toString()}'));
    }
  }

  void _onCancelReservation(CancelReservation event, Emitter<TableState> emit) {
    try {
      final updatedTables = state.tables.map((table) {
        if (table.id == event.tableId) {
          return table.copyWith(
            status: 'Available',
            reservationDetails: null,
            reservationTime: null,
            reservedBy: null,
          );
        }
        return table;
      }).toList();

      emit(state.copyWith(
        tables: updatedTables,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to cancel reservation: ${e.toString()}'));
    }
  }

  void _onMarkTableAsOccupied(MarkTableAsOccupied event, Emitter<TableState> emit) {
    try {
      final updatedTables = state.tables.map((table) {
        if (table.id == event.tableId) {
          return table.copyWith(
            status: 'Occupied',
            lastOccupied: DateTime.now(),
            occupiedBy: event.customerDetails,
          );
        }
        return table;
      }).toList();

      emit(state.copyWith(
        tables: updatedTables,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to mark table as occupied: ${e.toString()}'));
    }
  }

  void _onMarkTableAsAvailable(MarkTableAsAvailable event, Emitter<TableState> emit) {
    try {
      final updatedTables = state.tables.map((table) {
        if (table.id == event.tableId) {
          return table.copyWith(
            status: 'Available',
            cleaningStatus: 'Needs Cleaning',
            lastOccupied: null,
            occupiedBy: null,
          );
        }
        return table;
      }).toList();

      emit(state.copyWith(
        tables: updatedTables,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to mark table as available: ${e.toString()}'));
    }
  }

  void _onUpdateTableLayout(UpdateTableLayout event, Emitter<TableState> emit) {
    try {
      final updatedTables = state.tables.map((table) {
        if (table.id == event.tableId) {
          return table.copyWith(
            position: event.position,
            size: event.size,
          );
        }
        return table;
      }).toList();

      emit(state.copyWith(
        tables: updatedTables,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to update table layout: ${e.toString()}'));
    }
  }

  void _onUpdateTableFilters(UpdateTableFilters event, Emitter<TableState> emit) {
    emit(state.copyWith(
      filterStatus: event.filterStatus,
      filterSeats: event.filterSeats,
    ));
  }

  void _onUpdateCurrentFloor(UpdateCurrentFloor event, Emitter<TableState> emit) {
    if (state.availableFloors.contains(event.floor)) {
      emit(state.copyWith(currentFloor: event.floor));
    }
  }

  void _onViewTableBill(ViewTableBill event, Emitter<TableState> emit) {
    try {
      final table = state.tables.firstWhere(
        (t) => t.tableNumber == event.tableId,
        orElse: () => throw Exception('Table not found'),
      );

      if (table.status != 'Occupied') {
        emit(state.copyWith(error: 'No active bill for this table'));
        return;
      }

      final order = _getOrderForTable(event.tableId);
      if (order == null) {
        emit(state.copyWith(error: 'No order found for this table'));
        return;
      }

      emit(state.copyWith(
        currentOrder: order,
        error: null,
      ));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to view table bill: ${e.toString()}'));
    }
  }

  List<int> _getAvailableFloors(List<TableModel> tables) {
    final floors = tables.map((table) => table.floor).toSet().toList();
    floors.sort();
    return floors.isEmpty ? [1] : floors;
  }
}
