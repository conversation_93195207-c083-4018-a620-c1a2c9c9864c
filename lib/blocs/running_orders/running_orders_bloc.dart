import 'package:flutter_bloc/flutter_bloc.dart';
import 'running_orders_event.dart';
import 'running_orders_state.dart';

class RunningOrdersBloc extends Bloc<RunningOrdersEvent, RunningOrdersState> {
  RunningOrdersBloc() : super(const RunningOrdersState()) {
    on<FetchRunningOrders>(_onFetchRunningOrders);
    on<FilterByCategory>(_onFilterByCategory);
    on<FilterByStatus>(_onFilterByStatus);
    on<SortOrders>(_onSortOrders);
    on<RefreshOrders>(_onRefreshOrders);
  }

  // Sample data - replace with actual API call in production
  final List<Map<String, dynamic>> _sampleOrders = [
    {
      "id": "1001",
      "table": "T6",
      "status": "Ready",
      "type": "Dine-in",
      "items": [
        {"name": "Burger", "quantity": 2, "price": 12.99,"id": "1"},
        {"name": "Fries", "quantity": 1, "price": 4.99,"id": "2"},
        {"name": "Soda", "quantity": 3, "price": 1.99,"id": "3"},
      ],
      "time": "12:30 PM",
      "customer": "<PERSON> Doe"
    },
    {
      "id": "1002",
      "table": "T2",
      "status": "In Progress",
      "type": "Dine-in",
      "items": [
        {"name": "Pizza", "quantity": 1, "price": 14.99,"id": "4"},
        {"name": "Wings", "quantity": 2, "price": 8.99,"id": "5"},
        {"name": "Salad", "quantity": 1, "price": 6.99,"id": "6"},
      ],
      "time": "12:35 PM",
      "customer": "Jane Smith"
    },
    {
      "id": "1003",
      "table": "D1",
      "status": "Cooking",
      "type": "Delivery",
      "items": [
        {"name": "Pasta", "quantity": 1, "price": 11.99,"id": "7"},
        {"name": "Garlic Bread", "quantity": 1, "price": 5.99,"id": "8"},
        {"name": "Garlic Bread", "quantity": 1, "price": 5.99,"id": "9"},
      ],
      "time": "12:40 PM",
      "customer": "Alice Johnson"
    },
  ];

  Future<void> _onFetchRunningOrders(
    FetchRunningOrders event,
    Emitter<RunningOrdersState> emit,
  ) async {
    emit(state.copyWith(status: RunningOrdersStatus.loading));

    try {
      // Simulate API delay
      await Future.delayed(const Duration(seconds: 1));
      
      emit(state.copyWith(
        orders: _sampleOrders,
        status: RunningOrdersStatus.success,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: RunningOrdersStatus.failure,
        error: 'Failed to fetch orders: $e',
      ));
    }
  }

  void _onFilterByCategory(
    FilterByCategory event,
    Emitter<RunningOrdersState> emit,
  ) {
    final filteredOrders = event.category == "All"
        ? _sampleOrders
        : _sampleOrders
            .where((order) => order["type"] == event.category)
            .toList();

    emit(state.copyWith(
      orders: filteredOrders,
      selectedCategory: event.category,
    ));
  }

  void _onFilterByStatus(
    FilterByStatus event,
    Emitter<RunningOrdersState> emit,
  ) {
    final filteredOrders = event.statuses.isEmpty
        ? _sampleOrders
        : _sampleOrders
            .where((order) => event.statuses.contains(order["status"]))
            .toList();

    emit(state.copyWith(
      orders: filteredOrders,
      selectedStatuses: event.statuses,
    ));
  }

  void _onSortOrders(
    SortOrders event,
    Emitter<RunningOrdersState> emit,
  ) {
    final sortedOrders = List<Map<String, dynamic>>.from(state.orders);
    
    switch (event.sortBy) {
      case "newest":
        sortedOrders.sort((a, b) => b["time"].compareTo(a["time"]));
        break;
      case "oldest":
        sortedOrders.sort((a, b) => a["time"].compareTo(b["time"]));
        break;
      case "table":
        sortedOrders.sort((a, b) => a["table"].compareTo(b["table"]));
        break;
    }

    emit(state.copyWith(
      orders: sortedOrders,
      sortBy: event.sortBy,
    ));
  }

  Future<void> _onRefreshOrders(
    RefreshOrders event,
    Emitter<RunningOrdersState> emit,
  ) async {
    // In production, this would make a new API call
    // For now, we'll just re-emit the same sample data
    emit(state.copyWith(status: RunningOrdersStatus.loading));
    
    try {
      await Future.delayed(const Duration(seconds: 1));
      emit(state.copyWith(
        orders: _sampleOrders,
        status: RunningOrdersStatus.success,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: RunningOrdersStatus.failure,
        error: 'Failed to refresh orders: $e',
      ));
    }
  }

  Map<String, dynamic>? getOrderForTable(String tableNumber) {
    return _sampleOrders.firstWhere(
      (order) => order["table"] == tableNumber && 
                 order["status"] != "Completed",
      orElse: () => {},
    );
  }
}
