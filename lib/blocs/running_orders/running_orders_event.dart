import 'package:equatable/equatable.dart';

abstract class RunningOrdersEvent extends Equatable {
  const RunningOrdersEvent();

  @override
  List<Object> get props => [];
}

class FetchRunningOrders extends RunningOrdersEvent {}

class FilterByCategory extends RunningOrdersEvent {
  final String category;

  const FilterByCategory(this.category);

  @override
  List<Object> get props => [category];
}

class FilterByStatus extends RunningOrdersEvent {
  final List<String> statuses;

  const FilterByStatus(this.statuses);

  @override
  List<Object> get props => [statuses];
}

class SortOrders extends RunningOrdersEvent {
  final String sortBy;

  const SortOrders(this.sortBy);

  @override
  List<Object> get props => [sortBy];
}

class RefreshOrders extends RunningOrdersEvent {}