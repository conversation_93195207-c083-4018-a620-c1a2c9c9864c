import 'package:equatable/equatable.dart';

class OrderModel extends Equatable {
  final String id;
  final String tableId;
  final double total;
  final String status;
  final DateTime createdAt;
  final List<OrderItem> items;

  const OrderModel({
    required this.id,
    required this.tableId,
    required this.total,
    required this.status,
    required this.createdAt,
    required this.items,
  });

  @override
  List<Object?> get props => [id, tableId, total, status, createdAt, items];
}

class OrderItem extends Equatable {
  final String id;
  final String name;
  final double price;
  final int quantity;
  final Map<String, dynamic>? customization;

  const OrderItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    this.customization,
  });

  @override
  List<Object?> get props => [id, name, price, quantity, customization];
}