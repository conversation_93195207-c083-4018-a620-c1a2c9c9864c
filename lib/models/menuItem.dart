
class MenuItem {
  final String id;
  final String name;
  final double price;
  final String image;
  final String category;
  final String description;
  final List<String> ingredients;
  final int prepTime; // in minutes
  final double rating;
  final bool isSpicy;
  final List<String> dietaryInfo; // e.g., "Vegetarian", "Gluten-Free", etc.

  const MenuItem({
    required this.id,
    required this.name,
    required this.price,
    required this.image,
    required this.category,
    required this.description,
    required this.ingredients,
    required this.prepTime,
    this.rating = 4.5,
    this.isSpicy = false,
    this.dietaryInfo = const [],
  });
}
