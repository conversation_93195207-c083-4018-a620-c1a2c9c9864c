import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class TableModel extends Equatable {
  final int id;
  final int seats;
  final String status;
  final String cleaningStatus;
  final int crossAxisCellCount;
  final int mainAxisCellCount;
  final String location;
  final double price;
  final double minimumSpend;
  final double reservationFee;
  final List<String> features;
  final DateTime? reservationTime;
  final String? reservedBy;
  final Map<String, dynamic>? reservationDetails;
  final DateTime? lastCleaned;
  final DateTime? lastOccupied;
  final int averageOccupancyTime;
  final double popularityScore;
  final String section;
  final String tableNumber;
  final int floor;
  final Offset? position;
  final Size? size;
  final Map<String, dynamic>? occupiedBy;

  const TableModel({
    required this.id,
    required this.seats,
    required this.status,
    required this.cleaningStatus,
    required this.crossAxisCellCount,
    required this.mainAxisCellCount,
    required this.location,
    required this.price,
    required this.minimumSpend,
    required this.reservationFee,
    required this.features,
    this.reservationTime,
    this.reservedBy,
    this.reservationDetails,
    this.lastCleaned,
    this.lastOccupied,
    required this.averageOccupancyTime,
    required this.popularityScore,
    required this.section,
    required this.tableNumber,
    required this.floor,
    this.position,
    this.size,
    this.occupiedBy,
  });

  TableModel copyWith({
    int? id,
    int? seats,
    String? status,
    String? cleaningStatus,
    int? crossAxisCellCount,
    int? mainAxisCellCount,
    String? location,
    double? price,
    double? minimumSpend,
    double? reservationFee,
    List<String>? features,
    DateTime? reservationTime,
    String? reservedBy,
    Map<String, dynamic>? reservationDetails,
    DateTime? lastCleaned,
    DateTime? lastOccupied,
    int? averageOccupancyTime,
    double? popularityScore,
    String? section,
    String? tableNumber,
    int? floor,
    Offset? position,
    Size? size,
    Map<String, dynamic>? occupiedBy,
  }) {
    return TableModel(
      id: id ?? this.id,
      seats: seats ?? this.seats,
      status: status ?? this.status,
      cleaningStatus: cleaningStatus ?? this.cleaningStatus,
      crossAxisCellCount: crossAxisCellCount ?? this.crossAxisCellCount,
      mainAxisCellCount: mainAxisCellCount ?? this.mainAxisCellCount,
      location: location ?? this.location,
      price: price ?? this.price,
      minimumSpend: minimumSpend ?? this.minimumSpend,
      reservationFee: reservationFee ?? this.reservationFee,
      features: features ?? this.features,
      reservationTime: reservationTime ?? this.reservationTime,
      reservedBy: reservedBy ?? this.reservedBy,
      reservationDetails: reservationDetails ?? this.reservationDetails,
      lastCleaned: lastCleaned ?? this.lastCleaned,
      lastOccupied: lastOccupied ?? this.lastOccupied,
      averageOccupancyTime: averageOccupancyTime ?? this.averageOccupancyTime,
      popularityScore: popularityScore ?? this.popularityScore,
      section: section ?? this.section,
      tableNumber: tableNumber ?? this.tableNumber,
      floor: floor ?? this.floor,
      position: position ?? this.position,
      size: size ?? this.size,
      occupiedBy: occupiedBy ?? this.occupiedBy,
    );
  }

  @override
  List<Object?> get props => [
        id,
        seats,
        status,
        cleaningStatus,
        crossAxisCellCount,
        mainAxisCellCount,
        location,
        price,
        minimumSpend,
        reservationFee,
        features,
        reservationTime,
        reservedBy,
        reservationDetails,
        lastCleaned,
        lastOccupied,
        averageOccupancyTime,
        popularityScore,
        section,
        tableNumber,
        floor,
        position,
        size,
        occupiedBy,
      ];


}
