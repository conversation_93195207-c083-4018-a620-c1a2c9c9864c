class FoodItem {
  final String foodId;
  final String name;
  final String image;
  final double price;
  final String type;
  final String description;
  final String kind;
  int quantity;

  FoodItem({
    required this.name,
    required this.image,
    required this.price,
    required this.type,
    required this.description,
    required this.kind,
    required this.foodId,
    this.quantity = 1,
  });

  // Add a method to increment the quantity
  void incrementQuantity() {
    quantity++;
  }

  void decrementQuantity() {
    if (quantity > 0) {
      quantity--;
    }
  }

  void resetQuantity() {
    quantity = 0;
  }
}
