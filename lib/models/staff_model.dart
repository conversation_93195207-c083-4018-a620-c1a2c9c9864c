class StaffModel {
  final String id;
  final String firstName;
  final String? middleName;
  final String lastName;
  final String emailAddress;
  final String phoneNumber;
  final String address;
  final int? pin;
  final String? password;
  final String profileUrl;
  final List<String> branches;
  final StaffDaysAvailable staffDaysAvailable;
  final DefaultShiftTiming defaultShiftTiming;
  final List<StaffCertification> staffCertifications;
  final String? role;

  // Getter for id to maintain compatibilit

  // Getter for name to maintain compatibility
  String get name => '$firstName $middleName $lastName'.trim();

  StaffModel({
    this.id = '',
    required this.firstName,
    this.middleName,
    required this.lastName,
    required this.emailAddress,
    required this.phoneNumber,
    required this.address,
    this.pin,
    this.password,
    required this.profileUrl,
    required this.branches,
    required this.staffDaysAvailable,
    required this.defaultShiftTiming,
    required this.staffCertifications,
    this.role,
  });

  factory StaffModel.fromJson(Map<String, dynamic> json) {
    return StaffModel(
      id: json['staffId'] ?? '',
      firstName: json['firstName'],
      middleName: json['middleName'],
      lastName: json['lastName'],
      emailAddress: json['emailAddress'],
      phoneNumber: json['phoneNumber'],
      address: json['address'],
      pin: json['pin'],
      password: json['password'],
      profileUrl: json['profileUrl'],
      branches: List<String>.from(json['branches'] ?? []),
      staffDaysAvailable:
          StaffDaysAvailable.fromJson(json['staffDaysAvailable'] ?? {}),
      defaultShiftTiming:
          DefaultShiftTiming.fromJson(json['defaultShiftTiming'] ?? {}),
      staffCertifications: (json['staffCertifications'] as List<dynamic>? ?? [])
          .map((e) => StaffCertification.fromJson(e))
          .toList(),
      role: json['role'] ?? 'Staff',
    );
  }

  Map<String, dynamic> toJson() => {
        'firstName': firstName,
        'middleName': middleName,
        'lastName': lastName,
        'emailAddress': emailAddress,
        'phoneNumber': phoneNumber,
        'address': address,
        'pin': pin,
        'password': password,
        'profileUrl': profileUrl,
        'branches': branches,
        'staffDaysAvailable': staffDaysAvailable.toJson(),
        'defaultShiftTiming': defaultShiftTiming.toJson(),
        'staffCertifications':
            staffCertifications.map((e) => e.toJson()).toList(),
        if (role != null) 'role': role,
      };
}

class StaffDaysAvailable {
  final Map<String, String> days;

  StaffDaysAvailable({required this.days});

  factory StaffDaysAvailable.fromJson(Map<String, dynamic> json) {
    return StaffDaysAvailable(
      days: Map<String, String>.from(json['days'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() => {
        'days': days,
      };
}

class DefaultShiftTiming {
  final Map<String, DayShiftTiming> weeklyTimings;

  DefaultShiftTiming({required this.weeklyTimings});

  factory DefaultShiftTiming.fromJson(Map<String, dynamic> json) {
    final timings = <String, DayShiftTiming>{};
    (json['weeklyTimings'] ?? {}).forEach((k, v) {
      timings[k] = DayShiftTiming.fromJson(v);
    });
    return DefaultShiftTiming(weeklyTimings: timings);
  }

  Map<String, dynamic> toJson() => {
        'weeklyTimings': weeklyTimings.map((k, v) => MapEntry(k, v.toJson())),
      };
}

class DayShiftTiming {
  final ShiftDetail morning;
  final ShiftDetail noon;
  final ShiftDetail night;

  DayShiftTiming(
      {required this.morning, required this.noon, required this.night});

  factory DayShiftTiming.fromJson(Map<String, dynamic> json) {
    return DayShiftTiming(
      morning: ShiftDetail.fromJson(json['morning'] ?? {}),
      noon: ShiftDetail.fromJson(json['noon'] ?? {}),
      night: ShiftDetail.fromJson(json['night'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() => {
        'morning': morning.toJson(),
        'noon': noon.toJson(),
        'night': night.toJson(),
      };
}

class ShiftDetail {
  final bool isActive;
  final String? clockInTime;
  final String? clockOutTime;

  ShiftDetail({required this.isActive, this.clockInTime, this.clockOutTime});

  factory ShiftDetail.fromJson(Map<String, dynamic> json) {
    return ShiftDetail(
      isActive: json['isActive'] ?? false,
      clockInTime: json['clockInTime'],
      clockOutTime: json['clockOutTime'],
    );
  }

  Map<String, dynamic> toJson() => {
        'isActive': isActive,
        if (clockInTime != null) 'clockInTime': clockInTime,
        if (clockOutTime != null) 'clockOutTime': clockOutTime,
      };
}

class StaffCertification {
  final String certId;
  final String certName;
  final String attachmentUrl;
  final String? expiryDate;

  StaffCertification({
    required this.certId,
    required this.certName,
    required this.attachmentUrl,
    this.expiryDate,
  });

  factory StaffCertification.fromJson(Map<String, dynamic> json) {
    return StaffCertification(
      certId: json['certId'],
      certName: json['certName'],
      attachmentUrl: json['attachmentUrl'],
      expiryDate: json['expiryDate'],
    );
  }

  Map<String, dynamic> toJson() => {
        'certId': certId,
        'certName': certName,
        'attachmentUrl': attachmentUrl,
        if (expiryDate != null) 'expiryDate': expiryDate,
      };
}
