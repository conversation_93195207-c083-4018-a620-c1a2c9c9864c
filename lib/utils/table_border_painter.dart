
import 'dart:math';

import 'package:flutter/material.dart';

class TableBorderPainter extends CustomPainter {
  final int seats;
  final Color color;
  final double strokeWidth;

  TableBorderPainter({
    required this.seats,
    required this.color,
    this.strokeWidth = 2.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    final double width = size.width;
    final double height = size.height;
    final double centerX = width / 2;
    final double centerY = height / 2;

    // Adjust table size based on seat count to prevent overflow
    double tableWidth;
    double tableHeight;

    // Calculate appropriate table dimensions based on seat count
    switch (seats) {
      case 2:
        tableWidth = width * 0.5;
        tableHeight = height * 0.45;
        break;
      case 4:
        tableWidth = width * 0.45;
        tableHeight = height * 0.45;
        break;
      case 6:
        tableWidth = width * 0.55;
        tableHeight = height * 0.45;
        break;
      case 8:
        tableWidth = width * 0.65;
        tableHeight = height * 0.45;
        break;
      default:
        tableWidth = width * 0.5;
        tableHeight = height * 0.45;
    }

    // Ensure table fits within the canvas
    tableWidth = min(tableWidth, width * 0.9);
    tableHeight = min(tableHeight, height * 0.7);

    Path path = Path();
    path.addRRect(RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset(centerX, centerY),
        width: tableWidth,
        height: tableHeight,
      ),
      Radius.circular(8),
    ));

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) =>
      oldDelegate is TableBorderPainter &&
          (oldDelegate.seats != seats ||
              oldDelegate.color != color ||
              oldDelegate.strokeWidth != strokeWidth);
}