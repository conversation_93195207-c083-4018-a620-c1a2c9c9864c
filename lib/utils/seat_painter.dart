import 'dart:math';

import 'package:flutter/material.dart';

class SeatPainter extends CustomPainter {
  final int seatCount;
  final Color color;
  static const double seatWidth = 40.0;
  static const double seatHeight = 8.0;

  SeatPainter({required this.seatCount, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // Calculate appropriate table dimensions and seat offsets
    double tableWidth;
    double tableHeight = size.height * 0.45;

    // Determine table width based on seat count
    switch (seatCount) {
      case 2:
        tableWidth = size.width * 0.35;
        break;
      case 4:
        tableWidth = size.width * 0.45;
        break;
      case 6:
        tableWidth = size.width * 0.55;
        break;
      case 8:
        tableWidth = size.width * 0.65;
        break;
      default:
        tableWidth = size.width * 0.5;
    }

    // Ensure table fits within the canvas
    tableWidth = min(tableWidth, size.width * 0.9);
    tableHeight = min(tableHeight, size.height * 0.7);

    // Calculate seat positions based on table dimensions
    switch (seatCount) {
      case 2:
      // One seat at top, one at bottom
        _drawSeat(canvas, paint, centerX - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX - seatWidth / 2, centerY + tableHeight / 2);
        break;
      case 4:
      // Two seats at top, two at bottom
        _drawSeat(canvas, paint, centerX - tableWidth / 4 - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX + tableWidth / 4 - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX - tableWidth / 4 - seatWidth / 2, centerY + tableHeight / 2);
        _drawSeat(canvas, paint, centerX + tableWidth / 4 - seatWidth / 2, centerY + tableHeight / 2);
        break;
      case 6:
      // Three seats at top, three at bottom
        _drawSeat(canvas, paint, centerX - tableWidth * 0.33 - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX + tableWidth * 0.33 - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX - tableWidth * 0.33 - seatWidth / 2, centerY + tableHeight / 2);
        _drawSeat(canvas, paint, centerX - seatWidth / 2, centerY + tableHeight / 2);
        _drawSeat(canvas, paint, centerX + tableWidth * 0.33 - seatWidth / 2, centerY + tableHeight / 2);
        break;
      case 8:
      // Four seats at top, four at bottom
        _drawSeat(canvas, paint, centerX - tableWidth * 0.375 - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX - tableWidth * 0.125 - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX + tableWidth * 0.125 - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX + tableWidth * 0.375 - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX - tableWidth * 0.375 - seatWidth / 2, centerY + tableHeight / 2);
        _drawSeat(canvas, paint, centerX - tableWidth * 0.125 - seatWidth / 2, centerY + tableHeight / 2);
        _drawSeat(canvas, paint, centerX + tableWidth * 0.125 - seatWidth / 2, centerY + tableHeight / 2);
        _drawSeat(canvas, paint, centerX + tableWidth * 0.375 - seatWidth / 2, centerY + tableHeight / 2);
        break;
      default:
      // Default to two seats (one top, one bottom)
        _drawSeat(canvas, paint, centerX - seatWidth / 2, centerY - tableHeight / 2 - seatHeight);
        _drawSeat(canvas, paint, centerX - seatWidth / 2, centerY + tableHeight / 2);
    }
  }

  void _drawSeat(Canvas canvas, Paint paint, double x, double y) {
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(x, y, seatWidth, seatHeight),
        const Radius.circular(4),
      ),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) =>
      oldDelegate is SeatPainter &&
          (oldDelegate.seatCount != seatCount ||
              oldDelegate.color != color);
}