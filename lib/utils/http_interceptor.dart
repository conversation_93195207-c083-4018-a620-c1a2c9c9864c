import 'package:http_interceptor/http_interceptor.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

import '../config/env_config.dart';

// Global callback for token expiration
typedef TokenExpiredCallback = void Function();
TokenExpiredCallback? _globalTokenExpiredCallback;

// Function to set the global token expired callback
void setTokenExpiredCallback(TokenExpiredCallback callback) {
  _globalTokenExpiredCallback = callback;
}

class CustomInterceptor implements InterceptorContract {
  @override
  bool shouldInterceptRequest() => true;

  @override
  bool shouldInterceptResponse() => true;

  @override
  Future<BaseRequest> interceptRequest({required BaseRequest request}) async {
    // Add standard headers
    request.headers["x-host"] = EnvConfig.xHost;

    request.headers["Content-Type"] = "application/json";

    // Add auth token if available
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('access-token');
    if (token != null) {
      request.headers["Authorization"] = "Bearer $token";
      request.headers["Cookie"] = "access-token=$token";
    }

    return request;
  }

  @override
  Future<BaseResponse> interceptResponse(
      {required BaseResponse response}) async {
    // Check for 401 Unauthorized (token expired)
    if (response.statusCode == 401) {
      debugPrint(
          '🚫 HTTP Interceptor: 401 Unauthorized - Token may be expired');
      debugPrint('🚫 HTTP Interceptor: Request URL: ${response.request?.url}');
      debugPrint('🚫 HTTP Interceptor: Response body: ${response.toString()}');

      // Clear the expired token
      final prefs = await SharedPreferences.getInstance();
      final tokenBeforeClear = prefs.getString('access-token');
      debugPrint(
          '🚫 HTTP Interceptor: Clearing token: ${tokenBeforeClear != null ? 'EXISTS' : 'NULL'}');

      await prefs.remove('access-token');
      await prefs.remove('token-timestamp');

      debugPrint('🚫 HTTP Interceptor: Token cleared due to 401 response');

      // Trigger immediate token expiration callback
      if (_globalTokenExpiredCallback != null) {
        debugPrint('🔄 HTTP Interceptor: Triggering token expiration callback');
        _globalTokenExpiredCallback!();
      }
    }

    // Check for and save new tokens from response
    if (response.headers.containsKey('set-cookie')) {
      final cookieHeader = response.headers['set-cookie'];
      if (cookieHeader != null) {
        final token = _extractTokenFromCookies(cookieHeader);
        if (token != null) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('access-token', token);
          await prefs.setString(
              'token-timestamp', DateTime.now().toIso8601String());
          debugPrint('🔐 HTTP Interceptor: New token saved with timestamp');
        }
      }
    }
    return response;
  }

  // Helper to extract token from cookies
  String? _extractTokenFromCookies(String cookieHeader) {
    final cookieParts = cookieHeader.split(';');
    for (var part in cookieParts) {
      part = part.trim();
      if (part.startsWith('access-token=')) {
        return part.substring('access-token='.length);
      }
    }
    return null;
  }
}
