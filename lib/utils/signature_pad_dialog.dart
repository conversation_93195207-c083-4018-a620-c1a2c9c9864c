import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../blocs/checklist/signature_bloc.dart';
import '../../blocs/checklist/signature_event.dart';
import 'dart:ui' as ui;

class SignaturePadDialog extends StatefulWidget {
  final String itemTitle;
  final Function(List<List<Offset>>) onSignatureSaved;

  const SignaturePadDialog({
    Key? key,
    required this.itemTitle,
    required this.onSignatureSaved,
  }) : super(key: key);

  @override
  State<SignaturePadDialog> createState() => _SignaturePadDialogState();
}

class _SignaturePadDialogState extends State<SignaturePadDialog> {
  List<List<Offset>> _signaturePoints = [[]];
  int _currentStrokeIndex = 0;
  bool _isDrawing = false;
  bool _isEmpty = true;

  @override
  void initState() {
    super.initState();
    // Load existing signature if available
    final existingSignature = context.read<SignatureBloc>().state.getSignaturePoints(widget.itemTitle);
    if (existingSignature.isNotEmpty) {
      setState(() {
        _signaturePoints = List.from(existingSignature);
        _currentStrokeIndex = _signaturePoints.length - 1;
        _isEmpty = false;
      });
    }
  }

  void _onPanStart(DragStartDetails details) {
    setState(() {
      if (_currentStrokeIndex >= _signaturePoints.length) {
        _signaturePoints.add([]);
      }

      final RenderBox box = context.findRenderObject() as RenderBox;
      final Offset localPosition = box.globalToLocal(details.globalPosition);
      _signaturePoints[_currentStrokeIndex].add(localPosition);
      _isDrawing = true;
      _isEmpty = false;
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDrawing) return;

    setState(() {
      final RenderBox box = context.findRenderObject() as RenderBox;
      final Offset localPosition = box.globalToLocal(details.globalPosition);
      _signaturePoints[_currentStrokeIndex].add(localPosition);
    });
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isDrawing = false;
      _currentStrokeIndex = _signaturePoints.length;
      _signaturePoints.add([]);
    });
  }

  void _clearSignature() {
    setState(() {
      _signaturePoints = [[]];
      _currentStrokeIndex = 0;
      _isEmpty = true;
    });
  }

  void _saveSignature() {
    // Remove empty points lists
    final cleanedPoints = _signaturePoints.where((points) => points.isNotEmpty).toList();

    if (cleanedPoints.isNotEmpty) {
      // Save to the SignatureBloc
      context.read<SignatureBloc>().add(UpdateSignature(
        widget.itemTitle,
        cleanedPoints.expand((points) => points).toList(),
      ));

      // Callback to parent
      widget.onSignatureSaved(cleanedPoints);

      // Close dialog
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check for landscape mode
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    // Adjust dialog size based on orientation
    final dialogWidth = isLandscape
        ? MediaQuery.of(context).size.width * 0.6
        : MediaQuery.of(context).size.width * 0.9;

    final dialogHeight = isLandscape
        ? MediaQuery.of(context).size.height * 0.6
        : MediaQuery.of(context).size.height * 0.5;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: dialogWidth,
        height: dialogHeight,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              decoration: BoxDecoration(
                color: Color(0xFF1E88E5),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isLandscape ? 8 : 12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.draw,
                      color: Colors.white,
                      size: isLandscape ? 20 : 24,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Signature',
                          style: GoogleFonts.poppins(
                            fontSize: isLandscape ? 18 : 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'Sign to confirm ${widget.itemTitle}',
                          style: GoogleFonts.poppins(
                            fontSize: isLandscape ? 14 : 16,
                            color: Colors.white70,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Signature Area
            Expanded(
              child: Container(
                margin: EdgeInsets.all(isLandscape ? 16 : 20),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: GestureDetector(
                    onPanStart: _onPanStart,
                    onPanUpdate: _onPanUpdate,
                    onPanEnd: _onPanEnd,
                    child: CustomPaint(
                      size: Size.infinite,
                      painter: SignaturePainter(
                        points: _signaturePoints,
                      ),
                      child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        alignment: Alignment.center,
                        child: _isEmpty
                            ? Text(
                          'Draw your signature here',
                          style: GoogleFonts.poppins(
                            fontSize: isLandscape ? 14 : 16,
                            color: Colors.grey[400],
                          ),
                        )
                            : null,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Action Buttons
            Padding(
              padding: EdgeInsets.all(isLandscape ? 16 : 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    onPressed: _clearSignature,
                    icon: Icon(
                      Icons.refresh,
                      size: isLandscape ? 18 : 20,
                    ),
                    label: Text(
                      'Clear',
                      style: GoogleFonts.poppins(
                        fontSize: isLandscape ? 14 : 16,
                      ),
                    ),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey[700],
                    ),
                  ),
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[400],
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            horizontal: isLandscape ? 16 : 20,
                            vertical: isLandscape ? 12 : 14,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: GoogleFonts.poppins(
                            fontSize: isLandscape ? 14 : 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: _isEmpty ? null : _saveSignature,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFF43A047),
                          foregroundColor: Colors.white,
                          disabledBackgroundColor: Color(0xFF43A047).withOpacity(0.4),
                          padding: EdgeInsets.symmetric(
                            horizontal: isLandscape ? 16 : 20,
                            vertical: isLandscape ? 12 : 14,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Save',
                          style: GoogleFonts.poppins(
                            fontSize: isLandscape ? 14 : 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SignaturePainter extends CustomPainter {
  final List<List<Offset>> points;

  SignaturePainter({required this.points});

  @override
  void paint(Canvas canvas, Size size) {
    // Draw signature lines
    final Paint paint = Paint()
      ..color = Colors.black
      ..strokeCap = StrokeCap.round
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    // Draw the signature strokes
    for (final pointList in points) {
      if (pointList.length < 2) continue;

      final Path path = Path();
      path.moveTo(pointList.first.dx, pointList.first.dy);

      for (int i = 1; i < pointList.length; i++) {
        path.lineTo(pointList[i].dx, pointList[i].dy);
      }

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(SignaturePainter oldDelegate) => true;
}
