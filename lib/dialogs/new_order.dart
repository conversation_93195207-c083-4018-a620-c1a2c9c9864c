
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../router/router_constants.dart';
import '../widgets/number_picker.dart';

class ShowOrderDialog extends StatefulWidget {
  final String tableNumber;
  final String orderId;
  const ShowOrderDialog({super.key, required this.tableNumber, required this.orderId});

  @override
  State<ShowOrderDialog> createState() => _ShowOrderDialogState();
}

class _ShowOrderDialogState extends State<ShowOrderDialog> {
  int _guestCount = 1;
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor:
      Color.fromRGBO(27, 27, 27, 1.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      title: const Text(
        'Take Order Confirmation',
        style: TextStyle(
            fontSize: 22,
            color: Colors.white,
            fontWeight: FontWeight.bold),
      ),
      content: Padding(
        padding: const EdgeInsets.symmetric(
            vertical: 5.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'You are about to take this order. Please confirm.',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 16,
                  color: Colors.white),
            ),
            const SizedBox(height: 16),
            Text(
              'Order ID: ${widget.orderId}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Table Number: ${widget.tableNumber}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: Colors.blue,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment:
              MainAxisAlignment.spaceEvenly,
              children: [
                const Text(
                  'Enter No Of Guests: ',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.white),
                ),
                NumberPicker(onChanged: (value) {
                  setState(() {
                    _guestCount = value;
                  });
                }),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context)
                .pop(); // Close the dialog
          },
          child: const Text(
            'Cancel',
            style: TextStyle(
                color: Colors.red, fontSize: 16),
          ),
        ),
        ElevatedButton(
          onPressed: () {
            GoRouter.of(context).goNamed(
              RouterConstants.pos,
              queryParameters: {
                'tableNumber': widget.tableNumber,
                'orderId': widget.orderId,
              },
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            shape: RoundedRectangleBorder(
              borderRadius:
              BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(
                horizontal: 10, vertical: 10),
          ),
          child: const Text(
            'Confirm',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
}

