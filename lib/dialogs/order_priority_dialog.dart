import 'package:flutter/material.dart';
class OrderPriorityDialog extends StatelessWidget {
  final String orderType;
  final String tableNumber;

  const OrderPriorityDialog({
    Key? key,
    required this.orderType,
    required this.tableNumber,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Order Priority'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Normal Priority'),
            onTap: () => Navigator.of(context).pop(1),
          ),
          ListTile(
            title: const Text('High Priority'),
            onTap: () => Navigator.of(context).pop(2),
          ),
          ListTile(
            title: const Text('Urgent Priority'),
            onTap: () => Navigator.of(context).pop(3),
          ),
        ],
      ),
    );
  }
}
