import 'package:flutter/material.dart';

class Rush<PERSON>rderManager extends StatefulWidget {
  @override
  _RushOrderManagerState createState() => _RushOrderManagerState();
}

class _RushOrderManagerState extends State<RushOrderManager> {
  bool isRushEnabled = false; // Track Rush Order status
  double rushOrderFee = 5.00; // Default rush order fee
  int rushPreparationTime = 15; // Default delay time (minutes)

  void _toggleRushOrder(bool value) {
    setState(() {
      isRushEnabled = value;
    });

    if (isRushEnabled) {
      _applyRushDelay();
    } else {
      _removeRushDelay();
    }
  }

  void _applyRushDelay() {
    // Logic to delay all orders by `rushPreparationTime` minutes
    print("All orders will be delayed by $rushPreparationTime minutes.");
  }

  void _removeRushDelay() {
    // Logic to remove delay from all orders
    print("Rush order mode disabled. Orders will proceed normally.");
  }

  void _editRushFee() {
    TextEditingController feeController =
    TextEditingController(text: rushOrderFee.toStringAsFixed(2));

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Set Rush Order Fee"),
          content: TextField(
            controller: feeController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(labelText: "Enter new fee (\$)"),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text("Cancel"),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  rushOrderFee =
                      double.tryParse(feeController.text) ?? rushOrderFee;
                });
                Navigator.pop(context);
              },
              child: const Text("Save"),
            ),
          ],
        );
      },
    );
  }

  void _editRushTime() {
    TextEditingController timeController =
    TextEditingController(text: rushPreparationTime.toString());

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Set Rush Preparation Time"),
          content: TextField(
            controller: timeController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
                labelText: "Enter time in minutes"),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text("Cancel"),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  rushPreparationTime =
                      int.tryParse(timeController.text) ?? rushPreparationTime;
                  if (isRushEnabled) {
                    _applyRushDelay();
                  }
                });
                Navigator.pop(context);
              },
              child: const Text("Save"),
            ),
          ],
        );
      },
    );
  }


  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          const Icon(Icons.speed_outlined, color: Colors.red),
          const SizedBox(width: 8),
          const Text("Rush Order Management"),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Enable/Disable Rush Orders
          SwitchListTile(
            title: const Text("Enable Rush Orders"),
            subtitle: Text(isRushEnabled ? "Active" : "Inactive"),
            value: isRushEnabled,
            onChanged: _toggleRushOrder,
          ),
          ListTile(
            leading: const Icon(Icons.timer_outlined),
            title: const Text("Set Rush Order Fee"),
            subtitle: Text("Current: \$${rushOrderFee.toStringAsFixed(2)}"),
            onTap: _editRushFee,
          ),
          ListTile(
            leading: const Icon(Icons.schedule),
            title: const Text("Rush Preparation Time"),
            subtitle: Text("Current: $rushPreparationTime min"),
            onTap: _editRushTime,
          ),
          ListTile(
            leading: const Icon(Icons.list_outlined),
            title: const Text("View Active Rush Orders"),
            onTap: () {
              // Navigate to active rush orders
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          child: const Text("Close"),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ],
    );
  }
}